// @ts-ignore
import * as SQLite from 'expo-sqlite';

const db = SQLite.openDatabaseSync('vetassist_chat.db');

export interface Conversation {
  id: number;
  created_at: string;
  title: string;
}

export interface Message {
  id: number;
  conversation_id: number;
  sender: 'user' | 'ai';
  text: string;
  timestamp: string;
  images?: string;
}

export function initDB() {
  db.execSync(`
    CREATE TABLE IF NOT EXISTS conversations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      title TEXT
    );
    CREATE TABLE IF NOT EXISTS messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      conversation_id INTEGER,
      sender TEXT,
      text TEXT,
      images TEXT,
      timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY(conversation_id) REFERENCES conversations(id)
    );
  `);

  // Migration: Add images column if it doesn't exist
  try {
    db.execSync(`
      ALTER TABLE messages ADD COLUMN images TEXT;
    `);
  } catch (error) {
    // Column already exists, ignore error
    console.log('Images column already exists or migration not needed');
  }
}

export function createConversation(title: string = ''): number {
  const result = db.runSync(
    'INSERT INTO conversations (title) VALUES (?);',
    [title]
  );
  return result.lastInsertRowId;
}

export function addMessage(conversation_id: number, sender: string, text: string, images?: string): number {
  const result = db.runSync(
    'INSERT INTO messages (conversation_id, sender, text, images) VALUES (?, ?, ?, ?);',
    [conversation_id, sender, text, images || null]
  );
  return result.lastInsertRowId;
}

export function getConversations(): Conversation[] {
  const result = db.getAllSync('SELECT * FROM conversations ORDER BY created_at DESC;');
  return result as Conversation[];
}

export function getMessagesForConversation(conversation_id: number): Message[] {
  const result = db.getAllSync(
    'SELECT * FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC;',
    [conversation_id]
  );
  return result as Message[];
}

export function updateConversationTitle(conversation_id: number, title: string): void {
  db.runSync('UPDATE conversations SET title = ? WHERE id = ?;', [title, conversation_id]);
}

export function deleteConversation(conversation_id: number): void {
  db.runSync('DELETE FROM messages WHERE conversation_id = ?;', [conversation_id]);
  db.runSync('DELETE FROM conversations WHERE id = ?;', [conversation_id]);
}
