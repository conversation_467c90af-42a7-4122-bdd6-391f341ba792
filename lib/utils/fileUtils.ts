import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

let viewDocument: any = null;
try {
  const documentsViewer = require('@react-native-documents/viewer');
  viewDocument = documentsViewer.viewDocument;
} catch (error) {
  console.log('react-native-documents/viewer not available - using fallback');
}

export type FileData = {
  uri: string;
  fileName: string;
  fileType: string;
  id: string;
  base64?: string;
}

export type StoredFileData = {
  fileName: string;
  fileType: string;
  id: string;
  base64: string;
}

export async function convertFileToBase64(fileUri: string): Promise<string> {
  try {
    const base64 = await FileSystem.readAsStringAsync(fileUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    return base64;
  } catch (error) {
    console.error('Error converting file to base64:', error);
    throw error;
  }
}

export async function prepareFilesForStorage(files: FileData[]): Promise<StoredFileData[]> {
  const storedFiles: StoredFileData[] = [];

  for (const file of files) {
    try {
      const base64 = await convertFileToBase64(file.uri);
      storedFiles.push({
        fileName: file.fileName,
        fileType: file.fileType,
        id: file.id,
        base64,
      });
    } catch (error) {
      console.error(`Error processing file ${file.fileName}:`, error);
    }
  }

  return storedFiles;
}

export function convertStoredFilesToDisplayFormat(storedFiles: StoredFileData[]): FileData[] {
  return storedFiles.map(file => ({
    uri: `data:${file.fileType};base64,${file.base64}`,
    fileName: file.fileName,
    fileType: file.fileType,
    id: file.id,
    base64: file.base64,
  }));
}

export function serializeFilesForDB(files: StoredFileData[]): string {
  return JSON.stringify(files);
}

export function deserializeFilesFromDB(filesJson: string): FileData[] {
  try {
    if (!filesJson) return [];
    const storedFiles: StoredFileData[] = JSON.parse(filesJson);
    return convertStoredFilesToDisplayFormat(storedFiles);
  } catch (error) {
    console.error('Error deserializing files from DB:', error);
    return [];
  }
}

export function isImageFile(fileType: string, fileName?: string): boolean {
  if (fileType && fileType.startsWith('image/')) {
    return true;
  }

  if (fileName) {
    const extension = fileName.toLowerCase().split('.').pop();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'tif'];
    return imageExtensions.includes(extension || '');
  }

  return false;
}

export function isDocumentFile(fileType: string): boolean {
  const documentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
  ];
  return documentTypes.includes(fileType);
}

export function getFileIcon(fileType: string, fileName?: string): string {
  if (isImageFile(fileType, fileName)) return 'image';
  if (fileType === 'application/pdf') return 'pdf';
  if (fileType.includes('word')) return 'document';
  if (fileType.includes('excel') || fileType.includes('spreadsheet')) return 'spreadsheet';
  if (fileType.includes('powerpoint') || fileType.includes('presentation')) return 'presentation';
  if (fileType === 'text/plain') return 'text';
  return 'file';
}

export async function openDocument(file: FileData): Promise<void> {
  try {
    if (!viewDocument) {
      Alert.alert(
        'Document Viewer Not Available',
        'This feature requires a development build. The document viewer is not available in Expo Go.\n\nTo use this feature, please build the app with "expo run:ios" or "expo run:android".',
        [{ text: 'OK' }]
      );
      return;
    }

    let documentUri = file.uri;

    if (file.uri.startsWith('data:')) {
      const base64Data = file.uri.split(',')[1];
      const fileExtension = getFileExtension(file.fileName);
      const tempFileUri = `${FileSystem.documentDirectory}temp_${Date.now()}${fileExtension}`;

      await FileSystem.writeAsStringAsync(tempFileUri, base64Data, {
        encoding: FileSystem.EncodingType.Base64,
      });

      documentUri = tempFileUri;

      setTimeout(() => {
        FileSystem.deleteAsync(tempFileUri, { idempotent: true });
      }, 10000);
    }

    await viewDocument({
      uri: documentUri,
      mimeType: file.fileType,
      headerTitle: file.fileName,
    });
  } catch (error) {
    console.error('Error opening document:', error);
    Alert.alert('Error', 'Could not open document. Please try again.');
  }
}

function getFileExtension(fileName: string): string {
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot) : '';
}
