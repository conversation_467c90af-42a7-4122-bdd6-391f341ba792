import { useTranslation } from 'react-i18next';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { useAuth } from '@/context/auth';

export const useLogout = () => {
  const { t } = useTranslation();
  const { logout } = useAuth();

  const handleLogout = async () => {
    const executeLogout = async () => {
      try {
        await logout(); // This clears tokens, user data, and updates React state
        router.replace('/welcome');
      } catch (error) {
        console.error('Error during logout:', error);
        throw error;
      }
    };

    Alert.alert(
      t('logout_confirmation_title'),
      t('logout_confirmation_message'),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('confirm'),
          style: 'destructive',
          onPress: executeLogout,
        },
      ],
      { cancelable: true }
    );
  };

  return { handleLogout };
};
