import { ChatHistoryItem } from '@/components/ui/chat-item/services';
import chatClient from '@/lib/sqlite/chatClient';
import type { Conversation, Message } from '@/lib/sqlite/chatStorage';

// Helper function to format time ago
export const formatTimeAgo = (dateString: string): string => {
  // SQLite CURRENT_TIMESTAMP returns UTC time in format 'YYYY-MM-DD HH:MM:SS'
  // We need to explicitly treat it as UTC to avoid timezone issues
  let date: Date;

  // Check if the dateString is in SQLite format (YYYY-MM-DD HH:MM:SS)
  if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    // Append 'Z' to indicate UTC timezone
    date = new Date(dateString + 'Z');
  } else {
    // Handle other date formats
    date = new Date(dateString);
  }

  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} min ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
};

// Helper function to determine section
export const getSection = (dateString: string): 'today' | 'yesterday' | 'older' => {
  // Use the same timezone handling as formatTimeAgo
  let date: Date;

  if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    date = new Date(dateString + 'Z');
  } else {
    date = new Date(dateString);
  }

  const now = new Date();

  // Compare dates by setting time to start of day to avoid timezone issues
  const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const nowStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const diffInDays = Math.floor((nowStart.getTime() - dateStart.getTime()) / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return 'today';
  if (diffInDays === 1) return 'yesterday';
  return 'older';
};

// Helper function to generate title from first message
export const generateChatTitle = (messages: Message[]): string => {
  if (messages.length === 0) return 'New Chat';

  const firstUserMessage = messages.find(m => m.sender === 'user');
  if (!firstUserMessage) return 'New Chat';

  // Take first 50 characters and add ellipsis if longer
  const title = firstUserMessage.text.trim();
  return title.length > 50 ? title.substring(0, 50) + '...' : title;
};

// Helper function to calculate chat duration (mock for now)
export const calculateChatDuration = (messages: Message[]): string | undefined => {
  if (messages.length < 2) return undefined;

  // Mock duration calculation - in real app you might calculate based on timestamps
  const messageCount = messages.length;
  if (messageCount > 10) return '20 min';
  if (messageCount > 5) return '10 min';
  return '5 min';
};

// Main function to get chat history from SQLite
export const getChatHistoryFromDB = (): ChatHistoryItem[] => {
  try {
    const conversations = chatClient.getConversations();

    return conversations.map((conv: Conversation) => {
      const messages = chatClient.getMessagesForConversation(conv.id);
      const title = conv.title || generateChatTitle(messages);
      const timeAgo = formatTimeAgo(conv.created_at);
      const section = getSection(conv.created_at);
      const duration = calculateChatDuration(messages);

      return {
        id: conv.id.toString(),
        title,
        timeAgo,
        duration,
        section,
      };
    });
  } catch (error) {
    console.error('Error fetching chat history:', error);
    return [];
  }
};

export const mockChatHistory: ChatHistoryItem[] = [
  {
    id: '1',
    title: 'Golden retriever grooming',
    timeAgo: '1 hour ago',
    section: 'today',
  },
  {
    id: '2',
    title: "Arlo's chocolate toxicity",
    timeAgo: '2 hours ago',
    duration: '20 min',
    section: 'today',
  },
  {
    id: '3',
    title: 'Puppy Biting Behaviour',
    timeAgo: '3 hours ago',
    section: 'today',
  },
];
