import { ChatHistoryItem } from '@/components/ui/chat-item/services';
import chatClient from '@/lib/sqlite/chatClient';
import type { Conversation, Message } from '@/lib/sqlite/chatStorage';

export enum ChatSection {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  OLDER = 'older',
}

export const formatTimeAgo = (dateString: string): string => {
  let date: Date;

  if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    date = new Date(dateString + 'Z');
  } else {
    date = new Date(dateString);
  }

  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} min ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
};

export const getSection = (dateString: string): ChatSection => {
  let date: Date;

  if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    date = new Date(dateString + 'Z');
  } else {
    date = new Date(dateString);
  }

  const now = new Date();

  const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const nowStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const diffInDays = Math.floor((nowStart.getTime() - dateStart.getTime()) / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return ChatSection.TODAY;
  if (diffInDays === 1) return ChatSection.YESTERDAY;
  return ChatSection.OLDER;
};

export const generateChatTitle = (messages: Message[]): string => {
  if (messages.length === 0) return 'New Chat';

  const firstUserMessage = messages.find(m => m.sender === 'user');
  if (!firstUserMessage) return 'New Chat';

  const title = firstUserMessage.text.trim();
  return title.length > 50 ? title.substring(0, 50) + '...' : title;
};

export const calculateChatDuration = (messages: Message[]): string | undefined => {
  if (messages.length < 2) return undefined;

  const messageCount = messages.length;
  if (messageCount > 10) return '20 min';
  if (messageCount > 5) return '10 min';
  return '5 min';
};

export const getChatHistoryFromDB = (): ChatHistoryItem[] => {
  try {
    const conversations = chatClient.getConversations();

    return conversations.map((conv: Conversation) => {
      const messages = chatClient.getMessagesForConversation(conv.id);
      const title = conv.title || generateChatTitle(messages);
      const timeAgo = formatTimeAgo(conv.created_at);
      const section = getSection(conv.created_at);
      const duration = calculateChatDuration(messages);

      return {
        id: conv.id.toString(),
        title,
        timeAgo,
        duration,
        section,
      };
    });
  } catch (error) {
    console.error('Error fetching chat history:', error);
    return [];
  }
};

export const mockChatHistory: ChatHistoryItem[] = [
  {
    id: '1',
    title: 'Golden retriever grooming',
    timeAgo: '1 hour ago',
    section: ChatSection.TODAY,
  },
  {
    id: '2',
    title: "Arlo's chocolate toxicity",
    timeAgo: '2 hours ago',
    duration: '20 min',
    section: ChatSection.TODAY,
  },
  {
    id: '3',
    title: 'Puppy Biting Behaviour',
    timeAgo: '3 hours ago',
    section: ChatSection.TODAY,
  },
];
