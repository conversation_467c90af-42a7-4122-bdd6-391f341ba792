import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, TextInput, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useFocusEffect } from 'expo-router';

import Header from '@/components/header';
import { SearchIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import ChatItem from '@/components/ui/chat-item';
import { ChatHistoryItem } from '@/components/ui/chat-item/services';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';

import { getChatHistoryFromDB } from './services';
import styles from './styles';

const {
  container,
  mainContainer,
  titleStyle,
  searchContainer,
  searchInputField,
  sectionHeader,
} = styles;

const { textInputView } = INPUT_STYLES;

const ChatHistoryScreen = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);

  const loadChatHistory = useCallback(() => {
    const history = getChatHistoryFromDB();
    setChatHistory(history);
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadChatHistory();
    }, [loadChatHistory])
  );

  const getFilteredChatHistory = (): ChatHistoryItem[] => {
    return chatHistory.filter((item: ChatHistoryItem) =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getGroupedChatHistory = () => {
    const filteredHistory = getFilteredChatHistory();
    return {
      today: filteredHistory.filter(item => item.section === 'today'),
      yesterday: filteredHistory.filter(item => item.section === 'yesterday'),
      older: filteredHistory.filter(item => item.section === 'older'),
    };
  };

  const handleChatSelect = (chatId: string) => {
    router.replace({
      pathname: '/(tabs)',
      params: { conversationId: chatId }
    });
  };

  const renderChatItem = ({ item }: { item: ChatHistoryItem }) => (
    <ChatItem
      item={item}
      onPress={handleChatSelect}
    />
  );

  const renderSectionHeader = (title: string) => (
    <View style={sectionHeader}>
      <TextTypes type='errorText' color={COLOURS.primary}>
        {title}
      </TextTypes>
    </View>
  );

  const createFlatListData = () => {
    const data: any[] = [];
    const groupedHistory = getGroupedChatHistory();

    if (groupedHistory.today.length > 0) {
      data.push({ type: 'header', title: 'Today' });
      data.push(...groupedHistory.today.map((item: ChatHistoryItem) => ({ type: 'item', ...item })));
    }

    if (groupedHistory.yesterday.length > 0) {
      data.push({ type: 'header', title: 'Yesterday' });
      data.push(...groupedHistory.yesterday.map((item: ChatHistoryItem) => ({ type: 'item', ...item })));
    }

    if (groupedHistory.older.length > 0) {
      data.push({ type: 'header', title: 'Older' });
      data.push(...groupedHistory.older.map((item: ChatHistoryItem) => ({ type: 'item', ...item })));
    }

    return data;
  };

  const renderFlatListItem = ({ item }: { item: any }) => {
    if (item.type === 'header') {
      return renderSectionHeader(item.title);
    }
    return renderChatItem({ item });
  };

  return (
    <SafeAreaView style={container}>
      <Header title='' />
      <View style={mainContainer}>
        <TextTypes customStyle={titleStyle} color={COLOURS.primary} type='h1'>
          {t('drawer_chat_history')}
        </TextTypes>

        <View style={searchContainer}>
          <View style={textInputView}>
            <SearchIcon />
            <TextInput
              style={searchInputField}
              placeholder='Search'
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={COLOURS.secondaryTint}
            />
          </View>
        </View>

        <FlatList
          data={createFlatListData()}
          keyExtractor={(item) => item.type === 'header' ? `header-${item.title}` : item.id}
          renderItem={renderFlatListItem}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </SafeAreaView>
  );
};

export default ChatHistoryScreen;
