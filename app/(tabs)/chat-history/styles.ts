import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  mainContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  titleStyle: {
    marginTop: 10,
    marginBottom: 16,
  },
  searchContainer: {
    marginBottom: 4,
  },
  searchInputField: {
    flex: 1,
    fontSize: 16,
    color: COLOURS.textBlack,
    marginLeft: 12,
  },
  sectionHeader: {
    backgroundColor: COLOURS.lightGreen,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginBottom: 16,
    marginTop: 20
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLOURS.tertiaryShade,
  },
  chatContent: {
    flex: 1,
  },
  chatTitle: {
    marginBottom: 4,
  },
  chatTime: {
    fontSize: 14,
  },
  timeContainer: {
    alignItems: 'flex-end',
  },
  chatDuration: {
    fontSize: 14,
  },
});

export default styles;
