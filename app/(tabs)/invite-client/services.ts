import { Share, Alert } from 'react-native';
import * as Clipboard from 'expo-clipboard';

// Mock UI Functions - No real API calls, just for UI demonstration
export const generateReferralCode = async (): Promise<string> => {
  // Simulate API delay for realistic UI experience
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Generate a mock referral code for UI demonstration
  const mockCodes = ['CORNWALL-2382', 'LONDON-4567', 'BRISTOL-8901', 'OXFORD-1234', 'MANCHESTER-5678'];
  const randomCode = mockCodes[Math.floor(Math.random() * mockCodes.length)];

  return randomCode;
};

// Share functionality - UI only
export const handleShareReferralCode = async (referralCode: string): Promise<void> => {
  try {
    const shareMessage = `Join <PERSON>et<PERSON> using my referral code: ${referralCode}\n\nDownload the app and connect with your vet for seamless care coordination.`;

    const result = await Share.share({
      message: shareMessage,
      title: 'VetAssist Referral Code',
    });

    if (result.action === Share.sharedAction) {
      console.log('Referral code shared successfully');
    }
  } catch (error) {
    console.error('Error sharing referral code:', error);
    Alert.alert('Error', 'Failed to share referral code');
  }
};

// Copy to clipboard functionality - UI only
export const handleCopyReferralCode = async (referralCode: string): Promise<void> => {
  try {
    await Clipboard.setStringAsync(referralCode);
    Alert.alert('Copied', 'Referral code copied to clipboard');
  } catch (error) {
    console.error('Error copying referral code:', error);
    Alert.alert('Error', 'Failed to copy referral code');
  }
};

// Utility functions for UI
export const isValidReferralCode = (code: string): boolean => {
  return Boolean(code && code.length > 0);
};

export const formatReferralCode = (code: string): string => {
  // Format the referral code for display
  return code.toUpperCase();
};
