import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';
import { FONTS } from '@/constants/fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  mainContainer: {
    flex: 1,
  },
  titleStyle: {
    marginTop: 10,
    marginBottom: 16,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: COLOURS.grey
  },
  headerSection: {
    marginBottom: 24,
  },
  titleText: {
    marginBottom: 8,
    fontWeight: '600',
  },
  subtitleText: {
    lineHeight: 20,
    fontFamily: FONTS.semiBold
  },
  stepSection: {
    marginBottom: 20,
    backgroundColor: COLOURS.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    shadowColor: COLOURS.black,
    shadowOpacity: 0.03,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  stepNumberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  stepNumber: {
    width: 26,
    height: 26,
    borderRadius: 16,
    backgroundColor: COLOURS.lightPrimaryBlue,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepTitle: {
    marginLeft: 2
  },
  generateButton: {
    backgroundColor: COLOURS.primary,
    borderRadius: 25,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  generateButtonText: {
    letterSpacing: 0.5,
    textTransform: 'uppercase'
  },
  actionButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 6,
    gap: 10
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 24,
    marginHorizontal: 4,
  },
  shareButton: {
    backgroundColor: COLOURS.disableColor,
  },
  qrButton: {
    backgroundColor: COLOURS.disableColor,
  },
  actionButtonText: {
    marginLeft: 6,
    textTransform: 'uppercase'
  },
  referralCodeSection: {
    marginTop: 8,
  },
  referralCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderColor: COLOURS.borderColor,
    borderWidth: 1,
    marginBottom: 16,
  },
  referralCodeText: {
    flex: 1,
    paddingLeft: 10,
    letterSpacing: 1,
  },
  copyButton: {
    backgroundColor: COLOURS.primary,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    padding: 11,
    marginLeft: 12,
  },
  benefitsList: {
    backgroundColor: COLOURS.lightPrimaryBlue,
    borderRadius: 12,
    padding: 16,
  },
  integrationInfoContainer: { 
    flex: 1, 
    marginTop: 8
  },
  benefitItem: {
    marginBottom: 8,
    flexDirection: 'row',
    gap: 6,
  },
  benefitText: {
    flex: 1,
    lineHeight: 18
  },
  integrationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    flex: 1
  },
  integrationTitle: {
    marginBottom: 4,
    lineHeight: 21,
    flex: 1
  },
  integrationSubtitle: {
    lineHeight: 18,
    marginTop: 4
  },
  shareInviteButton: {
    backgroundColor: COLOURS.darkPrimary,
    borderRadius: 25,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
    marginBottom: 32,
    marginHorizontal: 20,
  },
  shareInviteButtonText: {
    letterSpacing: 0.5,
    textTransform: 'uppercase'
  },
});

export default styles;
