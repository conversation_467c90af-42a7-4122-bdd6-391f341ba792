import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, Switch, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import Header from '@/components/header';
import { CheckCircle, Copy, QrCode1, Share } from '@/components/icons';
import TextTypes from '@/components/text-types';
import QRCodeModal from '@/components/ui/qr-code-modal';
import { COLOURS } from '@/constants/colours';

import {
  generateReferralCode,
  handleCopyReferralCode,
  handleShareReferralCode,
} from './services';
import styles from './styles';

const InviteClientScreen = () => {
  const { t } = useTranslation();
  const [referralCode, setReferralCode] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [practiceIntegrationEnabled, setPracticeIntegrationEnabled] =
    useState<boolean>(true);
  const [isSharing, setIsSharing] = useState<boolean>(false);
  const [qrModalVisible, setQrModalVisible] = useState<boolean>(false);

  const {
    container,
    mainContainer,
    titleStyle,
    scrollContainer,
    headerSection,
    subtitleText,
    stepSection,
    stepNumber,
    stepNumberContainer,
    stepTitle,
    generateButton,
    generateButtonText,
    actionButtonsRow,
    actionButton,
    shareButton,
    qrButton,
    actionButtonText,
    referralCodeSection,
    referralCodeContainer,
    referralCodeText,
    copyButton,
    benefitsList,
    benefitItem,
    benefitText,
    integrationHeader,
    integrationInfoContainer,
    integrationTitle,
    integrationSubtitle,
    shareInviteButton,
    shareInviteButtonText,
  } = styles;

  const handleGenerate = async () => {
    setIsGenerating(true);
    try {
      const code = await generateReferralCode();
      setReferralCode(code);
    } catch (error) {
      console.error('Error generating referral code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleShare = async () => {
    if (!referralCode) return;
    setIsSharing(true);
    try {
      await handleShareReferralCode(referralCode);
    } catch (error) {
      console.error('Error sharing referral code:', error);
    } finally {
      setIsSharing(false);
    }
  };

  const handleQRPress = () => {
    if (!referralCode) return;
    setQrModalVisible(true);
  };

  const handleCopyPress = () => {
    if (!referralCode) return;
    handleCopyReferralCode(referralCode);
  };

  return (
    <SafeAreaView style={container}>
      <Header title='VetAssist' />
      <View style={mainContainer}>
        <ScrollView
          style={scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <TextTypes customStyle={titleStyle} color={COLOURS.primary} type='h1'>
            {t('invite_client_title')}
          </TextTypes>

          {/* Header Section */}
          <View style={headerSection}>
            <TextTypes
              type='body1'
              color={COLOURS.secondaryTint}
              customStyle={subtitleText}
            >
              {t('invite_client_subtitle')}
            </TextTypes>
          </View>

          {/* Step 1: Generate Referral Code */}
          <View style={stepSection}>
            <View style={stepNumberContainer}>
              <View style={stepNumber}>
                <TextTypes type='h4' color={COLOURS.primary}>
                  1
                </TextTypes>
              </View>
              <TextTypes
                type='h4'
                color={COLOURS.primary}
                customStyle={stepTitle}
              >
                {t('generate_referral_code')}
              </TextTypes>
            </View>

            <TouchableOpacity
              style={generateButton}
              onPress={handleGenerate}
              disabled={isGenerating}
              activeOpacity={0.8}
            >
              <TextTypes
                type='h5'
                color={COLOURS.white}
                customStyle={generateButtonText}
              >
                {isGenerating
                  ? t('generating')
                  : referralCode
                    ? t('generate_new_code')
                    : t('generate_button')}
              </TextTypes>
            </TouchableOpacity>

            <View style={actionButtonsRow}>
              <TouchableOpacity
                style={[actionButton, referralCode && shareButton]}
                onPress={handleShare}
                disabled={isSharing}
                activeOpacity={0.8}
              >
                <Share color={COLOURS.primary} />
                <TextTypes
                  type='h5'
                  color={COLOURS.primary}
                  customStyle={actionButtonText}
                >
                  {t('share_button')}
                </TextTypes>
              </TouchableOpacity>

              <TouchableOpacity
                style={[actionButton, referralCode && qrButton]}
                onPress={handleQRPress}
                activeOpacity={0.8}
              >
                <QrCode1 width={16} height={16} color={COLOURS.primary} />
                <TextTypes
                  type='h5'
                  color={COLOURS.primary}
                  customStyle={actionButtonText}
                >
                  {t('open_qr_button')}
                </TextTypes>
              </TouchableOpacity>
            </View>
          </View>

          {/* Step 2: Your Referral Code */}
          <View style={stepSection}>
            <View style={stepNumberContainer}>
              <View style={stepNumber}>
                <TextTypes type='h4' color={COLOURS.primary}>
                  2
                </TextTypes>
              </View>
              <TextTypes
                type='h4'
                color={COLOURS.primary}
                customStyle={stepTitle}
              >
                {t('your_referral_code')}
              </TextTypes>
            </View>

            <View style={referralCodeSection}>
              <View style={referralCodeContainer}>
                <TextTypes
                  type='h4'
                  color={COLOURS.textBlack}
                  customStyle={referralCodeText}
                >
                  {referralCode}
                </TextTypes>
                <TouchableOpacity
                  style={copyButton}
                  onPress={handleCopyPress}
                  activeOpacity={0.8}
                >
                  <Copy width={24} height={24} color={COLOURS.white} />
                </TouchableOpacity>
              </View>

              <View style={benefitsList}>
                <View style={benefitItem}>
                  <CheckCircle width={16} height={16} color={COLOURS.primary} />
                  <TextTypes
                    type='small'
                    color={COLOURS.primary}
                    customStyle={benefitText}
                  >
                    {t('link_to_vet_profile')}
                  </TextTypes>
                </View>
                <View style={benefitItem}>
                  <CheckCircle width={16} height={16} color={COLOURS.primary} />
                  <TextTypes
                    type='small'
                    color={COLOURS.primary}
                    customStyle={benefitText}
                  >
                    {t('revenue_share')}
                  </TextTypes>
                </View>
              </View>
            </View>
          </View>

          {/* Step 3: Practice Integration */}
          <View style={stepSection}>
            <View style={stepNumberContainer}>
              <View style={stepNumber}>
                <TextTypes type='h4' color={COLOURS.primary}>
                  3
                </TextTypes>
              </View>
              <TextTypes
                type='h4'
                color={COLOURS.primary}
                customStyle={stepTitle}
              >
                {t('practice_integration')}
              </TextTypes>
            </View>

            <View style={integrationInfoContainer}>
              <View style={integrationHeader}>
                <TextTypes
                  type='h5'
                  color={COLOURS.textBlack}
                  customStyle={integrationTitle}
                >
                  {t('practice_integration_toggle')}
                </TextTypes>
                <Switch
                  value={referralCode ? practiceIntegrationEnabled : false}
                  onValueChange={
                    referralCode ? setPracticeIntegrationEnabled : undefined
                  }
                  disabled={!referralCode}
                  trackColor={{
                    false: COLOURS.borderColor,
                    true: COLOURS.primary,
                  }}
                  thumbColor={COLOURS.white}
                />
              </View>
              <TextTypes
                type='small'
                color={COLOURS.gray30}
                customStyle={integrationSubtitle}
              >
                {t('practice_integration_subtitle')}
              </TextTypes>
            </View>
          </View>
        </ScrollView>

        {/* Share Invite Button */}
        <TouchableOpacity
          style={[
            shareInviteButton,
            !referralCode && { backgroundColor: COLOURS.borderColor },
          ]}
          onPress={handleShare}
          disabled={isSharing || !referralCode}
          activeOpacity={0.8}
        >
          <TextTypes
            type='h5'
            color={referralCode ? COLOURS.white : COLOURS.grayIcon}
            customStyle={shareInviteButtonText}
          >
            {isSharing ? t('sharing') : t('share_invite_button')}
          </TextTypes>
        </TouchableOpacity>
        {/* QR Code Modal */}
        <QRCodeModal
          visible={qrModalVisible}
          onClose={() => setQrModalVisible(false)}
          referralCode={referralCode}
          title={t('qr_code_title')}
          subtitle={t('qr_code_subtitle')}
        />
      </View>
    </SafeAreaView>
  );
};

export default InviteClientScreen;
