import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import Header from '@/components/header';
import { LogoutIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import LanguageSelector from '@/components/ui/language-selector';
import { LANGUAGES } from '@/components/ui/language-selector/services';
import SettingsItem from '@/components/ui/settings-item';
import { COLOURS } from '@/constants/colours';
import { useLogout } from '@/hooks/useLogout';
import { changeAppLanguage } from './services';

import { aboutSettingsItems, accountSettingsItems } from './services';
import styles from './styles';

const {
  container,
  sectionHeader,
  logoutButton,
  logoutButtonText,
  divider,
  mainContainer,
  settingStyle,
} = styles;

const SettingsScreen = () => {
  const { t, i18n } = useTranslation();
  const { handleLogout } = useLogout();
  const [languageModalVisible, setLanguageModalVisible] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language);

  const handleSelectLanguage = async (langValue: string) => {
    setSelectedLanguage(langValue);
    setLanguageModalVisible(false);
    await changeAppLanguage(langValue, t);
  };

  return (
    <SafeAreaView style={container}>
      <Header />
      <View style={mainContainer}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <TextTypes
            customStyle={settingStyle}
            color={COLOURS.primary}
            type='h1'
          >
            {t('settings')}
          </TextTypes>
          <TextTypes
            customStyle={sectionHeader}
            type='h4'
            color={COLOURS.secondaryTint}
          >
            {t('account_preferences')}
          </TextTypes>
          {accountSettingsItems.map((item) => (
            <SettingsItem
              key={item.key}
              item={item}
              onPress={() => {
                if (item.key === 'language') {
                  setLanguageModalVisible(true);
                }
                // handle other keys if needed
              }}
            />
          ))}
          <View style={divider} />
          <TextTypes
            customStyle={sectionHeader}
            type='h4'
            color={COLOURS.secondaryTint}
          >
            {t('about_and_legal')}
          </TextTypes>
          {aboutSettingsItems.map((item) => (
            <SettingsItem
              key={item.key}
              item={item}
            />
          ))}
          <TouchableOpacity style={logoutButton} onPress={handleLogout}>
            <LogoutIcon />
            <TextTypes
              color={COLOURS.primary}
              type='h4'
              customStyle={logoutButtonText}
            >
              {t('logout')}
            </TextTypes>
          </TouchableOpacity>
        </ScrollView>
      </View>
      <LanguageSelector
        visible={languageModalVisible}
        onClose={() => setLanguageModalVisible(false)}
        languages={LANGUAGES}
        selectedLanguage={selectedLanguage}
        onSelect={handleSelectLanguage}
      />
    </SafeAreaView>
  );
};

export default SettingsScreen;
