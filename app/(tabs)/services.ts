import * as Clipboard from 'expo-clipboard';
import { Platform } from 'react-native';
import { generateAnswer } from '@/lib/rag/generateAnswer';
import { getUserPets } from '@/lib/user/userPets';

import chatClient from '@/lib/sqlite/chatClient';
import type { StoredUser } from '@/types/auth';
import type { ChatItem } from '@/types/chat';
import { prepareFilesForStorage, serializeFilesForDB, deserializeFilesFromDB } from '@/lib/utils/fileUtils';

// Types for service functions
export interface MessageFormatInput {
  id: number;
  sender: string;
  text: string;
  images?: string;
}

export interface UserMessage {
  id: string;
  sender: 'user';
  text: string;
  files?: Array<{
    uri: string;
    fileName: string;
    fileType: string;
    id: string;
  }>;
}

export interface AIMessage {
  id: string;
  sender: 'ai';
  text: string;
}

export interface ConversationLoadResult {
  conversationId: number;
  messages: ChatItem[];
}

// Constants
export const ANIMATION_WORD_THRESHOLD = 30;
export const ANIMATION_DELAY_MS = 40;
export const COPY_FEEDBACK_DURATION_MS = 1200;
export const MESSAGE_DELAY_MS = 10;
export const SCROLL_OFFSET = 999999;



// Error Messages
export const AI_ERROR_MESSAGE = 'Sorry, something went wrong. Please try again.';

// Platform detection utilities
export const getPlatformComponent = () => {
  return Platform.OS === 'android' ? 'View' : 'KeyboardAvoidingView';
};

export const isIOSPlatform = () => {
  return Platform.OS === 'ios';
};

// Message formatting utilities
export const formatMessages = (dbMessages: MessageFormatInput[]): ChatItem[] => {
  return dbMessages.map((m) => ({
    id: m.id.toString(),
    sender: m.sender,
    text: m.text,
    files: m.images ? deserializeFilesFromDB(m.images) : undefined,
  }));
};

export const createUserMessage = (input: string, files?: Array<{uri: string; fileName: string; fileType: string; id: string}>): UserMessage => {
  return {
    id: Date.now().toString(),
    sender: 'user',
    text: input,
    files,
  };
};

export const createAIMessage = (text: string): AIMessage => {
  return {
    id: (Date.now() + 1).toString(),
    sender: 'ai',
    text,
  };
};

export const generateConversationTitle = (input: string): string => {
  return input.length > 50 ? input.substring(0, 50) + '...' : input;
};

// Conversation management utilities
export const loadSpecificConversationData = (conversationId: number): ChatItem[] => {
  try {
    const dbMessages = chatClient.getMessagesForConversation(conversationId);
    return formatMessages(dbMessages);
  } catch (error) {
    console.error('Error loading specific conversation:', error);
    throw error;
  }
};

export const loadDefaultConversationData = (): ConversationLoadResult => {
  try {
    const { conversationId, messages: dbMessages } =
      chatClient.getOrCreateFirstConversationWithMessages();

    return {
      conversationId,
      messages: formatMessages(dbMessages),
    };
  } catch (error) {
    console.error('Error loading default conversation:', error);
    throw error;
  }
};

export const createNewConversation = (): number => {
  try {
    const newId = chatClient.createConversation('');
    if (!newId) throw new Error('Failed to create conversation');
    return newId;
  } catch (error) {
    console.error('Error creating new conversation:', error);
    throw error;
  }
};

export const parseConversationId = (paramConversationId: string | string[] | undefined): number | null => {
  if (!paramConversationId || paramConversationId === 'undefined') {
    return null;
  }

  const conversationId = parseInt(paramConversationId as string, 10);
  return isNaN(conversationId) ? null : conversationId;
};

// Pet management utilities (minimal functions for parent screen)
export const loadUserPetsData = async (userId: string, accessToken: string): Promise<any[]> => {
  try {
    const userPets = await getUserPets(userId, accessToken);
    return userPets || [];
  } catch (error) {
    console.error('Error loading pets:', error);
    return [];
  }
};

export const getDefaultSelectedPet = (pets: any[]): any | null => {
  return pets.length > 0 ? pets[0] : null;
};

// Quick actions utilities (minimal for parent screen)
export const generateQuickActions = (selectedPetName?: string, isVet?: boolean): string[] => {
  const petName = selectedPetName || 'my pet';
  return isVet ? [
    'Dermatology case',
    'Emergency case',
    'Diagnostic case',
    'General consultation',
    'Specialist referral',
  ] : [
    `Follow-up on ${petName}'s chocolate incident?`,
    `Can I get a summary of ${petName}'s last consultation?`,
  ];
};





// AI response utilities
export const generateAIResponse = async (
  user: StoredUser,
  messageText: string
): Promise<string> => {
  try {
    const data = await generateAnswer(user, messageText, '', []);
    return data.formatted_response || 'Sorry, I could not generate a response.';
  } catch (error) {
    console.error('Error generating AI answer:', error);
    return 'Sorry, something went wrong. Please try again.';
  }
};

export const shouldAnimateMessage = (text: string): boolean => {
  const words = text.split(' ');
  return words.length > ANIMATION_WORD_THRESHOLD;
};

export const splitTextIntoWords = (text: string): string[] => {
  return text.split(' ');
};

// Clipboard utilities
export const copyToClipboard = async (text: string): Promise<void> => {
  try {
    await Clipboard.setStringAsync(text);
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    throw error;
  }
};

// Database utilities
export const saveUserMessageToDB = async (conversationId: number, text: string, files?: Array<{uri: string; fileName: string; fileType: string; id: string}>): Promise<void> => {
  try {
    let filesData: string | undefined;

    if (files && files.length > 0) {
      const storedFiles = await prepareFilesForStorage(files);
      filesData = serializeFilesForDB(storedFiles);
    }

    chatClient.addMessage(conversationId, 'user', text || 'File message', filesData);
  } catch (error) {
    console.error('Error saving user message to DB:', error);
  }
};

export const saveAIMessageToDB = (conversationId: number, text: string): void => {
  try {
    chatClient.addMessage(conversationId, 'ai', text);
  } catch (error) {
    console.error('Error saving AI message to DB:', error);
  }
};

export const updateConversationTitleInDB = (conversationId: number, title: string): void => {
  try {
    chatClient.updateConversationTitle(conversationId, title);
  } catch (error) {
    console.error('Error updating conversation title:', error);
  }
};

// Validation utilities
export const isValidMessageText = (text: string): boolean => {
  return text.trim().length > 0;
};

export const isValidConversationId = (conversationId: number | null): boolean => {
  return conversationId !== null && conversationId > 0;
};



// Platform utilities
export const getPlatformBasedComponent = () => {
  return Platform.OS === 'android' ? 'View' : 'KeyboardAvoidingView';
};



// Animation utilities
export const createAnimationRevealFunction = (
  words: string[],
  onUpdate: (current: string) => void,
  onComplete: (fullText: string) => void,
  fullText: string
) => {
  let current = '';
  let i = 0;

  const revealNextWord = () => {
    if (i < words.length) {
      current = current ? current + ' ' + words[i] : words[i];
      onUpdate(current);
      i++;
      setTimeout(revealNextWord, ANIMATION_DELAY_MS);
    } else {
      onComplete(fullText);
    }
  };

  return revealNextWord;
};

// Timeout utilities
export const createDelayedCallback = (callback: () => void, delay: number) => {
  return setTimeout(callback, delay);
};

export const createDelayedStateUpdate = <T>(
  setter: (value: T) => void,
  value: T,
  delay: number
) => {
  return setTimeout(() => setter(value), delay);
};

// Array utilities
export const isEmptyArray = (arr: any[]): boolean => {
  return arr.length === 0;
};

export const hasItems = (arr: any[]): boolean => {
  return arr.length > 0;
};

// Keyboard offset utilities
export const getKeyboardVerticalOffset = (insects: any, isIOS: boolean): number | undefined => {
  return isIOS ? -insects.bottom : undefined;
};

// Message length utilities
export const isEmptyMessageList = (messages: any[]): boolean => {
  return messages.length === 0;
};

export const hasMessages = (messages: any[]): boolean => {
  return messages.length > 0;
};

// Pet selection utilities
export const isPetSelected = (pet: any, selectedPet: any): boolean => {
  return selectedPet?.id === pet.id;
};

export function getRecentPairs(messages: ChatItem[], maxPairs: number = 5): { qu: string; an: string }[] {
  const recentPairs: { qu: string; an: string }[] = [];
  let lastUser: ChatItem | null = null;
  for (let i = messages.length - 1; i >= 0 && recentPairs.length < maxPairs; i--) {
    const msg = messages[i];
    if (msg.sender === 'ai' && lastUser) {
      recentPairs.unshift({ qu: lastUser.text, an: msg.text });
      lastUser = null;
    } else if (msg.sender === 'user') {
      lastUser = msg;
    }
  }
  if (lastUser && recentPairs.length < maxPairs) {
    recentPairs.unshift({ qu: lastUser.text, an: '' });
  }
  return recentPairs;
}

export function extractJsonFromString(str: string): any | null {
  if (!str) return null;
  const match = str.match(/\{[\s\S]*?\}/);
  if (!match) return null;
  try {
    return JSON.parse(match[0]);
  } catch (e) {
    return null;
  }
}
