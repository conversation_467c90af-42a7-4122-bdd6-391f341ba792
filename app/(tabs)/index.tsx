import { useEffect, useRef, useState } from 'react';
import {
  FlatList,
  KeyboardAvoidingView,
  Platform,
  View,
} from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';

import Header from '@/components/header';
import Loader from '@/components/ui/Loader';
import ChatInterface from '@/components/ui/chat-interface';
import WelcomeScreen from '@/components/ui/welcome-screen';
import WelcomeVetScreen from '@/components/ui/welcome-vet-screen';
import UploadBottomSheet from '@/components/ui/upload-bottom-sheet';
import { takePhoto, pickImage, pickDocument } from '@/components/ui/upload-bottom-sheet/services';

import { COLOURS } from '@/constants/colours';
import { GLOBAL_STYLES } from '@/constants/globalStyles';
import { useAuth } from '@/context/auth';
import type { StoredUser } from '@/types/auth';
import type { ChatItem } from '@/types/chat';
import { getRecentPairs, extractJsonFromString } from './services';

import styles from './styles';
import {
  loadSpecificConversationData,
  loadDefaultConversationData,
  createNewConversation,
  parseConversationId,
  loadUserPetsData,
  getDefaultSelectedPet,
  generateQuickActions,
  copyToClipboard,
  saveUserMessageToDB,
  saveAIMessageToDB,
  updateConversationTitleInDB,
  isValidMessageText,
  isValidConversationId,
  createUserMessage,
  createAIMessage,
  generateConversationTitle,
  isIOSPlatform,
  COPY_FEEDBACK_DURATION_MS,
  MESSAGE_DELAY_MS,
  SCROLL_OFFSET,
  AI_ERROR_MESSAGE,
  createAnimationRevealFunction,
  isEmptyArray,
  getKeyboardVerticalOffset,
  createDelayedStateUpdate,
} from './services';
import { generateAnswer } from '@/lib/rag/generateAnswer';

export default function HomeScreen() {
  const { user, accessToken } = useAuth();
  const insects = useSafeAreaInsets();
  const router = useRouter();
  const params = useLocalSearchParams();

  // Destructure styles for cleaner usage
  const {
    loadingScreen,
    mainContainer,
    contentContainer,
  } = styles;
  const [messages, setMessages] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [animatingMessage, setAnimatingMessage] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList<any>>(null);
  const [currentConversationId, setCurrentConversationId] = useState<
    number | null
  >(null);
  const [copyFeedbackId, setCopyFeedbackId] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [pets, setPets] = useState<any[]>([]);
  const [selectedPet, setSelectedPet] = useState<any>(null);
  const [petsLoading, setPetsLoading] = useState(false);
  const [uploadBottomSheetVisible, setUploadBottomSheetVisible] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Array<{
    uri: string;
    fileName: string;
    fileType: string;
    id: string;
  }>>([]);

  // Load user pets
  const loadUserPets = async () => {
    if (!user?.id || !accessToken) return;

    try {
      setPetsLoading(true);
      const userPets = await loadUserPetsData(user.id, accessToken);
      setPets(userPets);

      // Set first pet as selected if available
      const defaultPet = getDefaultSelectedPet(userPets);
      if (defaultPet) {
        setSelectedPet(defaultPet);
      }
    } catch (error) {
      setPets([]);
    } finally {
      setPetsLoading(false);
    }
  };

  // Load a specific conversation by ID
  const loadSpecificConversation = (conversationId: number) => {
    try {
      const dbMessages = loadSpecificConversationData(conversationId);

      // Clear messages first to force re-render
      setMessages([]);
      setCurrentConversationId(conversationId);

      // Set messages after a small delay to ensure state update
      createDelayedStateUpdate(setMessages, dbMessages, MESSAGE_DELAY_MS);

      // Clear the param to prevent re-triggering
      router.setParams({ conversationId: undefined });
      setInitialLoading(false);
    } catch (error) {
      setInitialLoading(false);
    }
  };

  // Load or create the default conversation
  const loadDefaultConversation = () => {
    try {
      const { conversationId, messages } = loadDefaultConversationData();

      setCurrentConversationId(conversationId);
      setMessages(messages);
    } catch (error) {
      // Error already logged in service
    } finally {
      setInitialLoading(false);
    }
  };

  // Main conversation loading logic
  const handleConversationLoad = () => {
    const paramConversationId = params.conversationId;

    // If parameter is being cleared, keep current conversation
    if (paramConversationId === undefined && currentConversationId !== null) {
      return;
    }

    setInitialLoading(true);

    // Load specific conversation if ID is provided
    const conversationId = parseConversationId(paramConversationId);
    if (conversationId) {
      loadSpecificConversation(conversationId);
      return;
    }

    // Load default conversation
    loadDefaultConversation();
  };

  // Load conversation based on params or default
  useEffect(() => {
    handleConversationLoad();
  }, [params.conversationId, currentConversationId]);

  // Load pets when user and accessToken are available
  useEffect(() => {
    if (user?.id && accessToken) {
      loadUserPets();
    }
  }, [user?.id, accessToken]);



  useEffect(() => {
    if (!initialLoading && params.isNewChat === '1') {
      handleNewChat();
      router.setParams({ isNewChat: undefined });
    }
  }, [params.isNewChat, initialLoading]);

  const scrollToBottom = () => {
    flatListRef.current?.scrollToOffset({ offset: SCROLL_OFFSET, animated: true });
  };

  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  const handleNewChat = () => {
    // Prevent starting new chat if current chat has no messages (redundant)
    if (messages.length === 0) {
      return;
    }

    // Prevent starting new chat during AI response or message animation
    if (loading || isTyping || animatingMessage !== null) {
      return;
    }

    try {
      const newId = createNewConversation();
      setCurrentConversationId(newId);
      setMessages([]);
    } catch (e) {
      // Error already logged in service
    }
  };

  const addUserMessage = async (input: string, files?: Array<{uri: string; fileName: string; fileType: string; id: string}>) => {
    const userMessage = createUserMessage(input, files);
    setMessages((prev) => {
      const newMessages = [...prev, userMessage];

      // If this is the first user message, update conversation title
      if (isValidConversationId(currentConversationId) && prev.length === 0) {
        const title = generateConversationTitle(input || 'File message');
        updateConversationTitleInDB(currentConversationId!, title);
      }

      return newMessages;
    });

    if (isValidConversationId(currentConversationId)) {
      await saveUserMessageToDB(currentConversationId!, input, files);
    }
    return userMessage;
  };

  const addAIMessage = (text: string) => {
    const aiMessage = createAIMessage(text);
    setMessages((prev) => [...prev, aiMessage]);

    if (isValidConversationId(currentConversationId)) {
      saveAIMessageToDB(currentConversationId!, text);
    }
  };

  const animateAIMessage = (aiText: string, words: string[]) => {
    setAnimatingMessage('');
    setIsTyping(true);

    const revealNextWord = createAnimationRevealFunction(
      words,
      (current) => setAnimatingMessage(current),
      () => {
        addAIMessage(aiText);
        setAnimatingMessage(null);
        setIsTyping(false);
      },
      aiText
    );

    revealNextWord();
  };

  const handleAIResponse = async (userMessage: any) => {
    setLoading(true);
    scrollToBottom();
    try {
      const recentPairs = getRecentPairs(messages, 5);
      const data = await generateAnswer(
        user as StoredUser,
        userMessage.text,
        '',
        recentPairs
      );
      let aiText = 'Sorry, I could not generate a response.';
      let appointment = false;
      if (data.response?.LLM) {
        const parsed = extractJsonFromString(data.response.LLM);
        if (parsed && typeof parsed.response === 'string') {
          aiText = parsed.response;
          appointment = !!parsed.appointment;
        }
      }
      const words = aiText.split(' ');
      if (words.length > 30) {
        animateAIMessage(aiText, words);
      } else {
        addAIMessage(aiText);
      }
      // Optionally handle appointment flag here (e.g., show UI)
    } catch (error) {
      addAIMessage(AI_ERROR_MESSAGE);
    } finally {
      setLoading(false);
      scrollToBottom();
    }
  };

  const handleSend = async (messageText: string) => {
    if ((!isValidMessageText(messageText) && selectedFiles.length === 0) || !isValidConversationId(currentConversationId)) {
      return;
    }
    const userMessage = await addUserMessage(messageText, selectedFiles);
    setSelectedFiles([]); // Clear selected files after sending
    await handleAIResponse(userMessage);
  };

  const handleCopy = async (text: string, id: string) => {
    try {
      await copyToClipboard(text);
      setCopyFeedbackId(id);
      createDelayedStateUpdate(setCopyFeedbackId, null, COPY_FEEDBACK_DURATION_MS);
    } catch (error) {
      // Error already logged in service
    }
  };

  // Quick action buttons
  const quickActions = generateQuickActions(selectedPet?.name, user?.isVet);

  const handleQuickAction = (action: string) => {
    handleSend(action);
  };

  const handleAddPet = () => {
    // Navigate to add pet screen - you can implement this based on your routing
  };

  const handlePetSelect = (pet: any) => {
    setSelectedPet(pet);
  };

  // Vet action handlers
  const handleNewCase = () => {
    // Navigate to new case screen or start new case chat
    handleSend('I need help with a new case');
  };

  const handleOCRDiagnostics = () => {
    // Navigate to OCR & diagnostics screen
    console.log('OCR & diagnostics action');
  };

  const handleSpecialistReferral = () => {
    // Navigate to specialist referral screen
    console.log('Specialist referral action');
  };

  const handleInviteClient = () => {
    router.push('/(tabs)/invite-client');
  };

  // Upload handlers
  const handleAddPress = () => {
    setUploadBottomSheetVisible(true);
  };

  const handleCloseUploadSheet = () => {
    setUploadBottomSheetVisible(false);
  };

  const handleTakePhoto = async () => {
    try {
      const result = await takePhoto();
      if (result) {
        const imageWithId = {
          ...result,
          id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        };
        setSelectedFiles(prev => [...prev, imageWithId]);
        console.log('Photo taken:', result);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
    } finally {
      setUploadBottomSheetVisible(false);
    }
  };

  const handleAddImage = async () => {
    try {
      const result = await pickImage();
      if (result) {
        const imageWithId = {
          ...result,
          id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        };
        setSelectedFiles(prev => [...prev, imageWithId]);
        console.log('Image selected:', result);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
    } finally {
      setUploadBottomSheetVisible(false);
    }
  };

  const handleAddFile = async () => {
    try {
      const result = await pickDocument();
      if (result) {
        const fileWithId = {
          ...result,
          id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        };
        setSelectedFiles(prev => [...prev, fileWithId]);
        console.log('Document selected:', result);
      }
    } catch (error) {
      console.error('Error selecting document:', error);
    } finally {
      setUploadBottomSheetVisible(false);
    }
  };

  const handleRemoveFile = (fileId: string) => {
    setSelectedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const Component = Platform.OS == 'android' ? View : KeyboardAvoidingView;
  const isIOS = isIOSPlatform();

  if (initialLoading) {
    return (
      <View style={loadingScreen}>
        <Loader />
      </View>
    );
  }

  return (
    <Component
      style={mainContainer}
      behavior={isIOS ? 'padding' : undefined}
      keyboardVerticalOffset={getKeyboardVerticalOffset(insects, isIOS)}
    >
      <SafeAreaView edges={['top']} style={GLOBAL_STYLES.container}>
        <Header
          onNewChatPress={handleNewChat}
          newChatDisabled={messages.length === 0 || loading || isTyping || animatingMessage !== null}
        />
        <View style={[GLOBAL_STYLES.content__gray, contentContainer]}>
          {isEmptyArray(messages) ? (
            user?.isVet ? (
              <WelcomeVetScreen
                user={user}
                onNewCase={handleNewCase}
                onOCRDiagnostics={handleOCRDiagnostics}
                onSpecialistReferral={handleSpecialistReferral}
                onInviteClient={handleInviteClient}
              />
            ) : (
              <WelcomeScreen
                pets={pets}
                selectedPet={selectedPet}
                petsLoading={petsLoading}
                onPetSelect={handlePetSelect}
                onAddPet={handleAddPet}
              />
            )
          ) : null}

          <ChatInterface
            messages={messages}
            onSend={handleSend}
            loading={loading}
            animatingMessage={animatingMessage}
            isTyping={isTyping}
            copyFeedbackId={copyFeedbackId}
            handleCopy={handleCopy}
            insects={insects}
            COLOURS={COLOURS}
            flatListRef={flatListRef}
            scrollToBottom={scrollToBottom}
            quickActions={quickActions}
            onQuickAction={handleQuickAction}
            onAddPress={handleAddPress}
            selectedFiles={selectedFiles}
            onRemoveFile={handleRemoveFile}
          />
        </View>
      </SafeAreaView>

      <UploadBottomSheet
        visible={uploadBottomSheetVisible}
        onClose={handleCloseUploadSheet}
        onTakePhoto={handleTakePhoto}
        onAddImage={handleAddImage}
        onAddFile={handleAddFile}
      />
    </Component>
  );
}
