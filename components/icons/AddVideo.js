import { COLOURS } from "@/constants/colours"
import * as React from "react"
import Svg, { Path } from "react-native-svg"

function SvgAddVideo({ width = 29, height = 28, color = COLOURS.primary, ...props }) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 29 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.688 11.197c.198 0 .392.053.564.152a1.129 1.129 0 01.53.958v9.026a1.128 1.128 0 01-1.128 1.128 1.128 1.128 0 01-.497-.124l-4.016-2.008c-.032.876-.403 2.27-1.034 2.878a3.384 3.384 0 01-2.35.947H6.601a3.384 3.384 0 01-3.384-3.385v-9.01c0-.897.262-1.758.897-2.393a3.385 3.385 0 012.393-.991H14.5v2.256H6.508c-.299 0-.586.119-.797.33-.212.212-.237.5-.237.798v9.01a1.128 1.128 0 001.128 1.128h10.154a1.128 1.128 0 001.128-1.128v-7.897c0-.623.506-1.129 1.129-1.129 1.145 0 1.13.9 1.132.72l-.004.849 3.982-1.963c.172-.1.366-.152.564-.152zm-4.547 6.616l3.385 1.693v-5.37l-3.385 1.692v1.985z"
        fill={color}
      />
      <Path
        d="M14.5 8.375a1.128 1.128 0 010 2.256V8.375zM20.14 4.974h1.129a1.128 1.128 0 010 2.257h-1.128v1.128a1.128 1.128 0 01-2.257 0V7.23h-1.128a1.128 1.128 0 010-2.257h1.128V3.846a1.128 1.128 0 012.257 0v1.128z"
        fill={color}
      />
    </Svg>
  )
}

export default SvgAddVideo
