import { COLOURS } from "@/constants/colours"
import * as React from "react"
import Svg, { Path } from "react-native-svg"

function Share(props) {
  return (
    <Svg
      width={17}
      height={16}
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M8.029 2.195a.664.664 0 01.216-.144l.726.144 2.667 2.667a.667.667 0 01-.943.943L9.167 4.276v6.39a.667.667 0 01-1.334 0v-6.39L6.305 5.805a.667.667 0 11-.943-.943l2.667-2.667z"
        fill={COLOURS.primary}
      />
      <Path
        d="M8.245 2.05l.725.144a.665.665 0 00-.725-.143zM3.833 7.333a.667.667 0 10-1.333 0v5.334a2 2 0 002 2h8a2 2 0 002-2V7.333a.667.667 0 00-1.333 0v5.334a.667.667 0 01-.667.666h-8a.667.667 0 01-.667-.666V7.333z"
        fill={COLOURS.primary}
      />
    </Svg>
  )
}

export default Share
