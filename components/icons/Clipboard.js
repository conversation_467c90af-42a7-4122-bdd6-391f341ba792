import * as React from "react"
import Svg, { Path } from "react-native-svg"

function SvgClipboard({ width = 29, height = 28, color = '#256B74', ...props }) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 29 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M13.372 12.872c0-.623.505-1.129 1.128-1.129h4.513a1.128 1.128 0 010 2.257H14.5a1.128 1.128 0 01-1.128-1.128zM14.5 17.384a1.128 1.128 0 100 2.257h4.513a1.128 1.128 0 000-2.256H14.5zM8.859 12.872c0-.623.505-1.129 1.128-1.129h.011a1.128 1.128 0 010 2.257h-.01a1.128 1.128 0 01-1.129-1.128zM9.987 17.384a1.128 1.128 0 100 2.257h.011a1.128 1.128 0 100-2.256h-.01z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.115 1.59A2.256 2.256 0 008.86 3.846H7.731a3.385 3.385 0 00-3.385 3.385v14.666a3.385 3.385 0 003.385 3.385h13.538a3.385 3.385 0 003.385-3.385V7.231a3.385 3.385 0 00-3.385-3.385h-1.128a2.256 2.256 0 00-2.256-2.256h-6.77zm9.026 4.512a2.256 2.256 0 01-2.256 2.257h-6.77A2.256 2.256 0 018.86 6.102H7.731a1.128 1.128 0 00-1.128 1.129v14.666a1.128 1.128 0 001.128 1.129h13.538a1.128 1.128 0 001.129-1.129V7.231a1.128 1.128 0 00-1.129-1.129h-1.128zm-9.026 0h6.77V3.846h-6.77v2.256z"
        fill={color}
      />
    </Svg>
  )
}

export default SvgClipboard
