import { COLOURS } from "@/constants/colours"
import * as React from "react"
import Svg, { Path } from "react-native-svg"

function SvgAddUser({ width = 29, height = 28, color = COLOURS.primary, ...props }) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 29 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M22.398 17.384c.623 0 1.128.506 1.128 1.129v2.256h2.256a1.128 1.128 0 010 2.257h-2.256v2.256a1.128 1.128 0 11-2.257 0v-2.256h-2.256a1.128 1.128 0 110-2.257h2.256v-2.256c0-.623.505-1.129 1.129-1.129zM4.346 24.154a1.129 1.129 0 01-2.256 0h2.256z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.244 2.718a6.77 6.77 0 014.104 12.15c.946.418 1.828.98 2.612 1.671a1.128 1.128 0 11-1.492 1.692 7.898 7.898 0 00-13.122 5.923H2.09A10.157 10.157 0 018.066 14.9l.071-.032a6.77 6.77 0 014.106-12.15zm0 2.256a4.513 4.513 0 100 9.026 4.513 4.513 0 000-9.026z"
        fill={color}
      />
    </Svg>
  )
}

export default SvgAddUser
