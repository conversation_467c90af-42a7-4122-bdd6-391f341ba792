import { ArrowLeft, Language, Menu1, NewChat } from '../icons';
import TextTypes from '../text-types';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { TouchableOpacity, View } from 'react-native';

import { COLOURS } from '@/constants/colours';
import { useAuth } from '@/context/auth';

import { HeaderProps } from './services';
import styles from './styles';

const { proText } = styles;

const Header = ({
  noTitle,
  onNewChatPress,
  newChatDisabled,
  onBackPress,
  title = 'VetAssist',
}: HeaderProps) => {
  const { container, disabled } = styles;
  const navigation = useNavigation<DrawerNavigationProp<any>>();
  const { user } = useAuth();
  if (noTitle) {
    return (
      <View style={container}>
        <TouchableOpacity onPress={onBackPress}>
          <ArrowLeft color={COLOURS.primary} />
        </TouchableOpacity>
        <Language color={COLOURS.primary} />
      </View>
    );
  }
  return (
    <View style={container}>
      <TouchableOpacity onPress={() => navigation.openDrawer()}>
        <Menu1 color={COLOURS.primary} />
      </TouchableOpacity>
      <View style={styles.centerContainer}>
        <TextTypes type='h3' color={COLOURS.primary}>
          {title}
        </TextTypes>
        {user?.isVet && (
          <View style={proText}>
            <TextTypes type='errorText' color={COLOURS.greyLight}>
              Pro
            </TextTypes>
          </View>
        )}
      </View>
      <TouchableOpacity
        onPress={noTitle ? undefined : onNewChatPress}
        disabled={!!newChatDisabled}
        style={newChatDisabled ? disabled : undefined}
      >
        <NewChat color={COLOURS.primary} />
      </TouchableOpacity>
    </View>
  );
};

export default Header;
