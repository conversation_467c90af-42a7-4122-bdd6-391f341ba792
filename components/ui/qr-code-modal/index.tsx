import { Dimensions, Modal, TouchableOpacity, View } from 'react-native';
import QRCode from 'react-native-qrcode-svg';

import { Close } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import { QRCodeModalProps } from './services';
import styles from './styles';

const { width: screenWidth } = Dimensions.get('window');
const qrSize = Math.min(screenWidth * 0.6, 250);

export default function QRCodeModal({
  visible,
  onClose,
  referralCode,
  title = 'QR Code',
  subtitle = 'Scan this code to join VetAssist',
}: QRCodeModalProps) {
  const {
    modalOverlay,
    modalContainer,
    modalContent,
    header,
    closeButton,
    titleText,
    subtitleText,
    qrContainer,
    codeText,
  } = styles;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType='fade'
      onRequestClose={onClose}
    >
      <View style={modalOverlay}>
        <View style={modalContainer}>
          <View style={modalContent}>
            {/* Header */}
            <View style={header}>
              <View style={{ flex: 1 }}>
                <TextTypes
                  type='h3'
                  color={COLOURS.textBlack}
                  customStyle={titleText}
                >
                  {title}
                </TextTypes>
                <TextTypes
                  type='body'
                  color={COLOURS.greyContrast}
                  customStyle={subtitleText}
                >
                  {subtitle}
                </TextTypes>
              </View>
              <TouchableOpacity
                style={closeButton}
                onPress={onClose}
                activeOpacity={0.8}
              >
                <Close width={24} height={24} color={COLOURS.greyContrast} />
              </TouchableOpacity>
            </View>

            {/* QR Code */}
            <View style={qrContainer}>
              <QRCode
                value={referralCode}
                size={qrSize}
                color={COLOURS.textBlack}
                backgroundColor={COLOURS.white}
                logo={require('@/assets/images/logo-green--no-text.png')}
                logoSize={40}
                logoBackgroundColor={COLOURS.white}
                logoMargin={4}
                logoBorderRadius={8}
              />
            </View>

            {/* Referral Code Text */}
            <TextTypes
              type='h4'
              color={COLOURS.textBlack}
              customStyle={codeText}
            >
              {referralCode}
            </TextTypes>
          </View>
        </View>
      </View>
    </Modal>
  );
}
