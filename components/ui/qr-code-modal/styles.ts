import { StyleSheet, Dimensions } from 'react-native';
import { COLOURS } from '@/constants/colours';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: screenWidth * 0.9,
    maxWidth: 400,
    backgroundColor: COLOURS.white,
    borderRadius: 16,
    shadowColor: COLOURS.textBlack,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalContent: {
    padding: 24,
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%',
    marginBottom: 32,
  },
  closeButton: {
    padding: 4,
    marginLeft: 16,
  },
  titleText: {
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitleText: {
    fontSize: 14,
    lineHeight: 20,
  },
  qrContainer: {
    backgroundColor: COLOURS.white,
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: COLOURS.textBlack,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  codeText: {
    fontWeight: '600',
    letterSpacing: 1,
    marginBottom: 32,
    textAlign: 'center',
  },
});

export default styles;
