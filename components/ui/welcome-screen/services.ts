import { getS3SignedImageUrl } from '@/lib/vet247/documents';

// Types for WelcomeScreen component
export interface WelcomeScreenProps {
  pets: any[];
  selectedPet: any;
  petsLoading: boolean;
  quickActions: string[];
  onPetSelect: (pet: any) => void;
  onAddPet: () => void;
  onQuickAction: (action: string) => void;
}

// Pet-related utilities specific to welcome screen
export const shouldShowDefaultPet = (pets: any[]): boolean => {
  return pets.length === 0;
};

export const shouldShowPetGrid = (petsLoading: boolean): boolean => {
  return !petsLoading;
};

// Quick actions utilities
export const hasQuickActions = (quickActions: string[]): boolean => {
  return quickActions.length > 0;
};

// Pet card utilities
export const getPetCardKey = (pet: any, index: number): string => {
  return pet.id ? pet.id.toString() : index.toString();
};

// Validation utilities
export const isValidPet = (pet: any): boolean => {
  return pet && (pet.id || pet.name);
};

export const isValidQuickAction = (action: string): boolean => {
  return Boolean(action && action.trim().length > 0);
};

// UI Constants for WelcomeScreen
export const WELCOME_TITLE = 'Welcome to VetAssist';
export const PET_SELECTION_TITLE = 'Choose your pet to chat about:';
export const ADD_PET_TEXT = 'add pet';
export const DEFAULT_PET_FALLBACK_NAME = 'Arlo';
export const WELCOME_SUBTITLE_PREFIX = 'Ultimate! How is ';
export const WELCOME_SUBTITLE_SUFFIX = ' today?';

// Welcome screen utilities
export const getWelcomeSubtitle = (petName?: string): string => {
  const name = petName || DEFAULT_PET_FALLBACK_NAME;
  return `${WELCOME_SUBTITLE_PREFIX}${name}${WELCOME_SUBTITLE_SUFFIX}`;
};



// Array utilities
export const isEmptyArray = (array: any[]): boolean => {
  return !array || array.length === 0;
};

// Pet selection utilities
export const isPetSelected = (pet: any, selectedPet: any): boolean => {
  return selectedPet?.id === pet.id;
};

// Pet display utilities
export const getPetDisplayName = (pet: any): string => {
  return pet?.name || 'Pet';
};

export const getPetDisplayBreed = (pet: any): string => {
  return pet?.breed || pet?.species || 'Unknown breed';
};

export const getPetDisplayAge = (pet: any): string => {
  if (!pet?.dateOfBirth) {
    return pet?.age || 'Unknown age';
  }

  const birthDate = new Date(pet.dateOfBirth);
  const today = new Date();

  // Check if the birth date is valid
  if (isNaN(birthDate.getTime())) {
    return pet?.age || 'Unknown age';
  }

  // Calculate the difference in time
  const diffTime = today.getTime() - birthDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  // If less than 30 days, show in days
  if (diffDays < 30) {
    return diffDays === 1 ? '1 day old' : `${diffDays} days old`;
  }

  // If less than 12 months, show in months
  const diffMonths = Math.floor(diffDays / 30);
  if (diffMonths < 12) {
    return diffMonths === 1 ? '1 month old' : `${diffMonths} months old`;
  }

  // Show in years (compact format)
  const diffYears = Math.floor(diffDays / 365);
  const remainingMonths = Math.floor((diffDays % 365) / 30);

  if (remainingMonths === 0) {
    return `${diffYears}y old`;
  } else {
    return `${diffYears}y ${remainingMonths}m old`;
  }
};

export const getPetInitial = (pet: any): string => {
  return pet?.name?.charAt(0)?.toUpperCase() || 'P';
};

export const hasPetImage = (pet: any): boolean => {
  return !!(pet?.photo || pet?.petPicture);
};

export const getPetImageUri = async (pet: any): Promise<string | null> => {
  const uri = pet?.photo || pet?.petPicture;
  try {
    const signedUrl = await getS3SignedImageUrl(uri);
    if (signedUrl) {
      return signedUrl;
    }
  } catch (error) {}
  return null;
};
