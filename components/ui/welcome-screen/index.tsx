import { Image, ScrollView, TouchableOpacity, View } from 'react-native';
import { useState, useEffect } from 'react';

import { Add } from '@/components/icons';
import TextTypes from '@/components/text-types';
import Loader from '@/components/ui/Loader';
import { COLOURS } from '@/constants/colours';

import {
  ADD_PET_TEXT,
  PET_SELECTION_TITLE,
  WELCOME_TITLE,
  getPetDisplayAge,
  getPetDisplayBreed,
  getPetDisplayName,
  getPetImageUri,
  getPetInitial,
  getWelcomeSubtitle,
  hasPetImage,
  isEmptyArray,
} from './services';
import styles from './styles';

interface WelcomeScreenProps {
  pets: any[];
  selectedPet: any;
  petsLoading: boolean;
  onPetSelect: (pet: any) => void;
  onAddPet: () => void;
}

export default function WelcomeScreen({
  pets,
  selectedPet,
  petsLoading,
  onPetSelect,
  onAddPet,
}: WelcomeScreenProps) {
  // State to manage pet image URIs
  const [petImageUris, setPetImageUris] = useState<Record<string, string | null>>({});
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});

  // Load pet images when pets change
  useEffect(() => {
    const loadPetImages = async () => {
      if (!pets || pets.length === 0) return;

      for (const pet of pets) {
        const petId = pet.id || pet.name || 'default';

        if (hasPetImage(pet) && !petImageUris[petId] && !loadingImages[petId]) {
          // Set loading state
          setLoadingImages(prev => ({ ...prev, [petId]: true }));

          try {
            const uri = await getPetImageUri(pet);
            setPetImageUris(prev => ({ ...prev, [petId]: uri }));
          } catch (error) {
            console.error('Error loading pet image:', error);
            setPetImageUris(prev => ({ ...prev, [petId]: null }));
          } finally {
            setLoadingImages(prev => ({ ...prev, [petId]: false }));
          }
        }
      }
    };

    loadPetImages();
  }, [pets, petImageUris, loadingImages]);

  // Helper function to get pet image URI
  const getPetImageUriSync = (pet: any): string | null => {
    const petId = pet.id || pet.name || 'default';
    return petImageUris[petId] || null;
  };

  // Helper function to check if pet image is loading
  const isPetImageLoading = (pet: any): boolean => {
    const petId = pet.id || pet.name || 'default';
    return loadingImages[petId] || false;
  };
  // Destructure styles for cleaner usage
  const {
    welcomeContent,
    logoImage,
    welcomeTitle,
    welcomeSubtitle,
    petSelectionContainer,
    petSelectionTitle,
    petsGrid,
    petCard,
    petImage,
    petImagePlaceholder,
    petInitial,
    petName,
    petDetails,
    petAge,
    addPetCard,
    addPetText,
  } = styles;

  return (
    <ScrollView
      contentContainerStyle={welcomeContent}
      showsVerticalScrollIndicator={false}
    >
      {/* Logo */}
      <Image
        source={require('@/assets/images/logo-green--no-text.png')}
        style={logoImage}
      />

      {/* Welcome Message */}
      <TextTypes type='h3' color={COLOURS.primary} customStyle={welcomeTitle}>
        {WELCOME_TITLE}
      </TextTypes>
      <TextTypes
        type='h3'
        color={COLOURS.primary}
        customStyle={welcomeSubtitle}
      >
        {getWelcomeSubtitle(selectedPet?.name)}
      </TextTypes>

      {/* Pet Selection Section */}
      <View style={petSelectionContainer}>
        <TextTypes
          type='h5'
          color={COLOURS.gray30}
          customStyle={petSelectionTitle}
        >
          {PET_SELECTION_TITLE}
        </TextTypes>

        <View style={petsGrid}>
          {petsLoading ? (
            <Loader />
          ) : (
            <>
              {!isEmptyArray(pets) && pets.map((pet, index) => (
                    <TouchableOpacity
                      key={pet.id || index}
                      style={petCard}
                      onPress={() => onPetSelect(pet)}
                      activeOpacity={0.8}
                    >
                      {hasPetImage(pet) ? (
                        (() => {
                          const imageUri = getPetImageUriSync(pet);
                          const isLoading = isPetImageLoading(pet);

                          if (isLoading) {
                            return <View style={petImage} />; // Loading placeholder
                          }

                          if (imageUri) {
                            return <Image source={{ uri: imageUri }} style={petImage} />;
                          }

                          // Fallback to placeholder if image failed to load
                          return (
                            <View style={petImagePlaceholder}>
                              <TextTypes
                                type='h2'
                                color={COLOURS.white}
                                customStyle={petInitial}
                              >
                                {getPetInitial(pet)}
                              </TextTypes>
                            </View>
                          );
                        })()
                      ) : (
                        <View style={petImagePlaceholder}>
                          <TextTypes
                            type='h2'
                            color={COLOURS.white}
                            customStyle={petInitial}
                          >
                            {getPetInitial(pet)}
                          </TextTypes>
                        </View>
                      )}
                      <TextTypes
                        type='h2'
                        color={COLOURS.white}
                        customStyle={petName}
                      >
                        {getPetDisplayName(pet)}
                      </TextTypes>
                      <TextTypes
                        type='small'
                        color={COLOURS.white}
                        customStyle={petDetails}
                      >
                        {getPetDisplayBreed(pet)}
                      </TextTypes>
                      <TextTypes
                        type='small'
                        color={COLOURS.white}
                        customStyle={petAge}
                      >
                        {getPetDisplayAge(pet)}
                      </TextTypes>
                    </TouchableOpacity>
                  ))}

              {/* Add Pet Button */}
              <TouchableOpacity
                style={addPetCard}
                onPress={onAddPet}
                activeOpacity={0.8}
              >
                <Add width={40} height={40} color={COLOURS.primary} />
                <TextTypes
                  type='h3'
                  color={COLOURS.primary}
                  customStyle={addPetText}
                >
                  {ADD_PET_TEXT}
                </TextTypes>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </ScrollView>
  );
}
