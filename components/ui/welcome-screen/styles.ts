import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  welcomeContent: {
    paddingHorizontal: 20,
  },
  logoImage: {
    width: 70,
    height: 70,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginTop: 30,
  },
  welcomeTitle: {
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 5,
    fontSize: 24,
    fontWeight: '600',
  },
  welcomeSubtitle: {
    textAlign: 'center',
    marginBottom: 30,
    fontSize: 24,
    fontWeight: '600',
  },
  petSelectionContainer: {
    backgroundColor: COLOURS.white,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    padding: 16,
    shadowColor: COLOURS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 8,
    elevation: 2,
    alignItems: 'center',
  },
  petSelectionTitle: {
    marginBottom: 16,
  },
  petsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  petCard: {
    width: '48%',
    backgroundColor: COLOURS.primary,
    borderRadius: 34,
    alignItems: 'center',
    padding: 12,
    justifyContent: 'center',
  },
  petImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  petImagePlaceholder: {
    width: 44,
    height: 44,
    borderRadius: 25,
    backgroundColor: COLOURS.primaryTint,
    alignItems: 'center',
    justifyContent: 'center',
  },
  petInitial: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  petName: {
    textAlign: 'center',
    marginBottom: 4,
    marginTop: 6
  },
  petDetails: {
    textAlign: 'center',
    marginBottom: 4
  },
  petAge: {
    textAlign: 'center',
    fontSize: 12,
  },
  addPetCard: {
    width: '48%',
    backgroundColor: COLOURS.white,
    borderRadius: 34,
    borderWidth: 1.3,
    borderColor: COLOURS.borderColor,
    borderStyle: 'dashed',
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addPetText: {
    marginTop: 4
  },
});

export default styles;
