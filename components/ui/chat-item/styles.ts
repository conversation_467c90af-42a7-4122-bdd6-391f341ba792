import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatItemContainer: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
    paddingHorizontal: 8
  },
  avatarContainer: {
    marginRight: 6,
  },
  avatar: {
    width: 24,
    height: 24,
    borderRadius: 20,
    backgroundColor: COLOURS.tertiaryShade,
  },
  chatContent: {
    flex: 1,
  },
  bottomTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 7
  },
  timeContainer: {
    marginLeft: 4
  },
});

export default styles;
