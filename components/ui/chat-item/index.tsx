import React from 'react';
import { TouchableOpacity, View } from 'react-native';

import { Video } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import { ChatHistoryItem } from './services';
import styles from './styles';

const {
  chatItem,
  avatarContainer,
  avatar,
  chatContent,
  timeContainer,
  chatItemContainer,
  bottomTimeContainer,
} = styles;

interface ChatItemProps {
  item: ChatHistoryItem;
  onPress?: (chatId: string) => void;
}

const ChatItem: React.FC<ChatItemProps> = ({ item, onPress }) => {
  return (
    <TouchableOpacity
      style={chatItemContainer}
      onPress={() => onPress?.(item.id)}
    >
      <View style={chatItem}>
        <View style={avatarContainer}>
          <View style={avatar} />
        </View>
        <View style={chatContent}>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            {item.title}
          </TextTypes>
        </View>
      </View>
      <View style={bottomTimeContainer}>
        <TextTypes type='errorText' color={COLOURS.secondaryTint}>
          {item.timeAgo}
        </TextTypes>
        {item.duration && (
          <View style={chatItem}>
            <Video width={18} height={18} color={COLOURS.grayIcon} />
            <View style={timeContainer}>
              <TextTypes type='small' color={COLOURS.grayIcon}>
                {item.duration}
              </TextTypes>
            </View>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default ChatItem;
