import { DrawerContentComponentProps } from '@react-navigation/drawer';
import { useRouter } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { InfoIcon, VetAssistIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';
import { useAuth } from '@/context/auth';

import { DrawerMenuItem, getMenuItems } from './services';
import styles from './styles';

const {
  drawerContainer,
  logoContainer,
  menuItem,
  icon,
  menuTitle,
  badge,
  disabledMenuItem,
  disabledIcon,
  disabledText,
  registrationPrompt,
  bottomContainer,
  infoContainer,
  registrationText,
  completeRegistrationButton,
} = styles;

const DrawerMenu: React.FC<DrawerContentComponentProps> = () => {
  const insects = useSafeAreaInsets();
  const router = useRouter();
  const { t } = useTranslation();
  const { user } = useAuth();

  const menuItems = getMenuItems(!!user?.isVet);

  const handleMenuPress = (key: string) => {
    if (key === 'new-chat' || key === 'new-case') {
      router.push({ pathname: '/(tabs)', params: { isNewChat: '1' } });
    } else if (key === 'settings') {
      router.push('/(tabs)/settings');
    } else if (key === 'chat-history' || key === 'case-history') {
      router.push('/(tabs)/chat-history');
    } else if (key === 'ocr-diagnostics') {
      // TODO: Navigate to OCR & Diagnostics screen
      console.log('Navigate to OCR & Diagnostics');
    } else if (key === 'specialist-referral') {
      // TODO: Navigate to Specialist referral screen
      console.log('Navigate to Specialist referral');
    } else if (key === 'invite-client') {
      router.push('/(tabs)/invite-client');
    } else if (key === 'offer-consultation') {
      // TODO: Navigate to Offer consultation screen
      console.log('Navigate to Offer consultation');
    } else if (key === 'my-pets') {
      // TODO: Navigate to My pets screen
      console.log('Navigate to My pets');
    }
  };

  return (
    <View style={drawerContainer}>
      <View style={[logoContainer, { marginTop: insects.bottom + 30 }]}>
        <VetAssistIcon />
      </View>
      <View>
        {menuItems.map(
          ({
            key,
            icon: Icon,
            label,
            badgeCount,
            disabled,
          }: DrawerMenuItem) => (
            <TouchableOpacity
              key={key}
              style={[menuItem, disabled && disabledMenuItem]}
              onPress={() => !disabled && handleMenuPress(key)}
              disabled={disabled}
            >
              <Icon
                width={20}
                height={20}
                color={COLOURS.textBlack}
                style={[icon, disabled && disabledIcon]}
              />
              <TextTypes
                customStyle={[menuTitle, disabled && disabledText]}
                type='h5'
              >
                {t(label)}
              </TextTypes>
              {badgeCount !== undefined && (
                <View style={badge}>
                  <TextTypes color={COLOURS.white} type={'errorText'}>
                    {badgeCount}
                  </TextTypes>
                </View>
              )}
            </TouchableOpacity>
          )
        )}
      </View>

      {/* Vet Registration Prompt - Only show for vet users */}
      {user?.isVet && (
        <View style={bottomContainer}>
          <View style={registrationPrompt}>
            <View style={infoContainer}>
              <InfoIcon width={18} height={18} color={COLOURS.primary} />
              <TextTypes
                type='small'
                color={COLOURS.primary}
                customStyle={registrationText}
              >
                {t('vet_registration_prompt')}
              </TextTypes>
            </View>
            <TouchableOpacity
              style={completeRegistrationButton}
              onPress={() => {
                // TODO: Navigate to complete registration screen
                console.log('Navigate to complete registration');
              }}
            >
              <TextTypes type='h5' color={COLOURS.primary}>
                {t('completeRegistration').toUpperCase()}
              </TextTypes>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default DrawerMenu;
