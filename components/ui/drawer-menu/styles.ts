import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  drawerContainer: { flex: 1, backgroundColor: 'transparent', padding: 24 },
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
    paddingTop: 60,
    paddingHorizontal: 20,
    borderTopRightRadius: 24,
    borderBottomRightRadius: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 90,
    height: 90,
    marginBottom: 10,
    resizeMode: 'contain',
  },
  logoText: {
    fontSize: 32,
    fontWeight: '700',
    color: COLOURS.primary,
    letterSpacing: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
    position: 'relative',
  },
  icon: {
    fontSize: 22,
    marginLeft: 4,
    color: COLOURS.primary,
  },
  badge: {
    backgroundColor: COLOURS.tertiaryShade,
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    marginRight: 6
  },
  menuTitle: {
    marginHorizontal: 8,
    flex: 1,
    textAlign: 'left'
  },
  disabledMenuItem: {
    opacity: 0.5,
  },
  disabledIcon: {
    color: COLOURS.gray30,
  },
  disabledText: {
    color: COLOURS.gray30,
  },
  bottomContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 20
  },
  registrationPrompt: {
    backgroundColor: COLOURS.lightPrimaryBlue,
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
    marginHorizontal: 4,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  registrationText: {
    flex: 1,
    lineHeight: 18,
    marginLeft: 4
  },
  completeRegistrationButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 12
  },
});

export default styles;
