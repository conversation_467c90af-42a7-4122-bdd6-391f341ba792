import { ComponentType } from 'react';
import { AddChatIcon, PetMenuIcon, HistoryMenuIcon, SettingMenuIcon, Clipboard, AddUser, Timer, AddVideo } from '@/components/icons';

export type DrawerMenuItem = {
  key: string;
  icon: ComponentType<any>;
  label: string;
  badgeCount?: number;
  disabled?: boolean;
};

// Menu items for regular pet owners
export const petOwnerMenuItems: DrawerMenuItem[] = [
  {
    key: 'new-chat',
    icon: AddChatIcon,
    label: 'drawer_new_chat',
  },
  {
    key: 'my-pets',
    icon: PetMenuIcon,
    label: 'drawer_my_pets',
    badgeCount: 1,
  },
  {
    key: 'chat-history',
    icon: HistoryMenuIcon,
    label: 'drawer_chat_history',
  },
  {
    key: 'settings',
    icon: SettingMenuIcon,
    label: 'settings',
  },
];

// Menu items for vet professionals
export const vetMenuItems: DrawerMenuItem[] = [
  {
    key: 'new-case',
    icon: AddChatIcon,
    label: 'drawer_new_case',
  },
  {
    key: 'ocr-diagnostics',
    icon: Clipboard,
    label: 'drawer_ocr_diagnostics',
  },
  {
    key: 'specialist-referral',
    icon: AddUser,
    label: 'drawer_specialist_referral',
    disabled: true,
  },
  {
    key: 'invite-client',
    icon: AddUser,
    label: 'drawer_invite_client',
  },
  {
    key: 'case-history',
    icon: HistoryMenuIcon,
    label: 'drawer_case_history',
  },
  {
    key: 'offer-consultation',
    icon: AddVideo,
    label: 'drawer_offer_consultation',
    disabled: true,
  },
  {
    key: 'settings',
    icon: SettingMenuIcon,
    label: 'settings',
  },
];

// Function to get menu items based on user type
export const getMenuItems = (isVet: boolean): DrawerMenuItem[] => {
  return isVet ? vetMenuItems : petOwnerMenuItems;
};