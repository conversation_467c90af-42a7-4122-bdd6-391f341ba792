import React from 'react';
import {
  Modal,
  TouchableOpacity,
  View,
} from 'react-native';

import { AddPhoto, AddImage, AddDocument, ArrowLeft } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import styles from './styles';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface UploadBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  onTakePhoto: () => void;
  onAddImage: () => void;
  onAddFile: () => void;
}

export default function UploadBottomSheet({
  visible,
  onClose,
  onTakePhoto,
  onAddImage,
  onAddFile,
}: UploadBottomSheetProps) {
  const insects = useSafeAreaInsets();
  const {
    modalOverlay,
    bottomSheet,
    header,
    backButton,
    title,
    optionsContainer,
    optionButton,
    optionIcon,
    optionText,
  } = styles;

  return (
    <Modal
      visible={visible}
      animationType='slide'
      transparent
      onRequestClose={onClose}
    >
      <View style={modalOverlay}>
        <View style={[bottomSheet, {paddingBottom: insects.bottom + 25}]}>
          <View style={header}>
            <TouchableOpacity onPress={onClose} style={backButton}>
              <ArrowLeft color={COLOURS.primary} width={24} height={24} />
            </TouchableOpacity>
            <TextTypes type='h3' color={COLOURS.primary} customStyle={title}>
              Upload
            </TextTypes>
            <View style={backButton} />
          </View>

          <View style={optionsContainer}>
            <TouchableOpacity
              style={optionButton}
              onPress={onTakePhoto}
              activeOpacity={0.8}
            >
              <View style={optionIcon}>
                <AddPhoto color={COLOURS.primary} width={36} height={36} />
              </View>
              <TextTypes type='errorText' color={COLOURS.primary} customStyle={optionText}>
                Take photo
              </TextTypes>
            </TouchableOpacity>

            <TouchableOpacity
              style={optionButton}
              onPress={onAddImage}
              activeOpacity={0.8}
            >
              <View style={optionIcon}>
                <AddImage color={COLOURS.primary} width={36} height={36} />
              </View>
              <TextTypes type='errorText' color={COLOURS.primary} customStyle={optionText}>
                Add image
              </TextTypes>
            </TouchableOpacity>

            <TouchableOpacity
              style={optionButton}
              onPress={onAddFile}
              activeOpacity={0.8}
            >
              <View style={optionIcon}>
                <AddDocument color={COLOURS.primary} width={36} height={36} />
              </View>
              <TextTypes type='errorText' color={COLOURS.primary} customStyle={optionText}>
                Add file
              </TextTypes>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
