import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

export default StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: COLOURS.backgroundModal,
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: COLOURS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    paddingTop: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    textAlign: 'center',
    flex: 1,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    gap: 14
  },
  optionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    borderRadius: 16,
    paddingVertical: 10,
  },
  optionIcon: {
    marginBottom: 12,
  },
  optionText: {
    textAlign: 'center',
  },
});
