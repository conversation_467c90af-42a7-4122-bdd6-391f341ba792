import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  welcomeContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  logoImage: {
    width: 70,
    height: 70,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginTop: 30,
  },
  welcomeSubtitle: {
    textAlign: 'center',
    marginBottom: 14,
    marginTop: 15,
    marginHorizontal: 10
  },
  actionsContainer: {
    gap: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionCard: {
    flex: 1,
    borderRadius: 34,
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  primaryActionCard: {
    backgroundColor: COLOURS.primary,
    shadowColor: COLOURS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryActionCard: {
    backgroundColor: COLOURS.white,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    shadowColor: COLOURS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 8,
    elevation: 2,
  },
  actionIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 7,
    backgroundColor: COLOURS.lightPrimaryBlue,
  },
  actionTitle: {
    textAlign: 'center',
    marginBottom: 5,
    marginTop: 1,
    lineHeight: 18
  },
  actionSubtitle: {
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default styles;
