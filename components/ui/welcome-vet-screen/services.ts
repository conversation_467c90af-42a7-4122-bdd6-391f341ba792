// Types for WelcomeVetScreen component
export interface WelcomeVetScreenProps {
  user: any;
  onNewCase: () => void;
  onOCRDiagnostics: () => void;
  onSpecialistReferral: () => void;
  onInviteClient: () => void;
}

// UI Constants for WelcomeVetScreen
export const WELCOME_VET_SUBTITLE_PREFIX = 'Welcome ';
export const WELCOME_VET_SUBTITLE_SUFFIX = ', how can I assist with your cases today?';

// Action Card Constants
export const NEW_CASE_TEXT = 'New case';
export const NEW_CASE_SUBTITLE = 'Triage case or clinical query';

export const OCR_DIAGNOSTICS_TEXT = 'OCR &\ndiagnostics';
export const OCR_DIAGNOSTICS_SUBTITLE = 'Upload bloodwork, images, tables';

export const SPECIALIST_REFERRAL_TEXT = 'Specialist\nreferral';
export const SPECIALIST_REFERRAL_SUBTITLE = 'Connect with a VetAssist vet';

export const INVITE_CLIENT_TEXT = 'Invite a Client';
export const INVITE_CLIENT_SUBTITLE = 'Generate referral code or link';

// Welcome screen utilities
export const getWelcomeVetSubtitle = (doctorName?: string): string => {
  const name = 'Dr. ' + doctorName || 'Dr. Surname';
  return `${WELCOME_VET_SUBTITLE_PREFIX}${name}${WELCOME_VET_SUBTITLE_SUFFIX}`;
};

// Validation utilities
export const isValidUser = (user: any): boolean => {
  return user && (user.id || user.email);
};

export const isValidAction = (action: string): boolean => {
  return Boolean(action && action.trim().length > 0);
};

// Action handlers utilities
export const handleNewCaseAction = (): void => {
  // Logic for handling new case action
  console.log('New case action triggered');
};

export const handleOCRDiagnosticsAction = (): void => {
  // Logic for handling OCR & diagnostics action
  console.log('OCR & diagnostics action triggered');
};

export const handleSpecialistReferralAction = (): void => {
  // Logic for handling specialist referral action
  console.log('Specialist referral action triggered');
};

export const handleInviteClientAction = (): void => {
  // Logic for handling invite client action
  console.log('Invite client action triggered');
};

// User display utilities
export const getVetDisplayName = (user: any): string => {
  if (user?.title && user?.lastName) {
    return `${user.title} ${user.lastName}`;
  }
  if (user?.firstName && user?.lastName) {
    return `Dr. ${user.lastName}`;
  }
  return user?.firstName || user?.lastName || 'Dr. Surname';
};

export const getVetFullName = (user: any): string => {
  if (user?.firstName && user?.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }
  return user?.firstName || user?.lastName || 'Doctor';
};

// Case type utilities
export const getCaseTypes = (): string[] => {
  return [
    'Dermatology case',
    'Emergency case',
    'Diagnostic case',
    'General consultation',
    'Specialist referral',
  ];
};

export const isValidCaseType = (caseType: string): boolean => {
  const validTypes = getCaseTypes();
  return validTypes.includes(caseType);
};
