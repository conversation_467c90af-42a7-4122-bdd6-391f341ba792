import { Image, ScrollView, TouchableOpacity, View } from 'react-native';

import { AddUser,NewChat, Clipboard, AddVideo } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import {
  NEW_CASE_TEXT,
  NEW_CASE_SUBTITLE,
  OCR_DIAGNOSTICS_TEXT,
  OCR_DIAGNOSTICS_SUBTITLE,
  SPECIALIST_REFERRAL_TEXT,
  SPECIALIST_REFERRAL_SUBTITLE,
  INVITE_CLIENT_TEXT,
  INVITE_CLIENT_SUBTITLE,
  getWelcomeVetSubtitle,
} from './services';
import styles from './styles';

interface WelcomeVetScreenProps {
  user: any;
  onNewCase: () => void;
  onOCRDiagnostics: () => void;
  onSpecialistReferral: () => void;
  onInviteClient: () => void;
}

export default function WelcomeVetScreen({
  user,
  onNewCase,
  onOCRDiagnostics,
  onSpecialistReferral,
  onInviteClient,
}: WelcomeVetScreenProps) {
  // Destructure styles for cleaner usage
  const {
    welcomeContent,
    logoImage,
    welcomeSubtitle,
    actionsContainer,
    actionCard,
    primaryActionCard,
    secondaryActionCard,
    actionIconContainer,
    actionTitle,
    actionSubtitle,
    actionsGrid,
  } = styles;

  return (
    <ScrollView
      contentContainerStyle={welcomeContent}
      showsVerticalScrollIndicator={false}
    >
      {/* Logo */}
      <Image
        source={require('@/assets/images/logo-green--no-text.png')}
        style={logoImage}
      />
      <TextTypes
        type='h3'
        color={COLOURS.primary}
        customStyle={welcomeSubtitle}
      >
        {getWelcomeVetSubtitle(user?.firstName || 'Surname')}
      </TextTypes>

      {/* Action Cards */}
      <View style={actionsContainer}>
        {/* Primary Actions Row */}
        <View style={actionsGrid}>
          {/* New Case - Primary Action */}
          <TouchableOpacity
            style={[actionCard, primaryActionCard]}
            onPress={onNewCase}
            activeOpacity={0.8}
          >
            <View style={actionIconContainer}>
              <NewChat width={27} height={27} color={COLOURS.primary} />
            </View>
            <TextTypes
              type='h5'
              color={COLOURS.white}
              customStyle={actionTitle}
            >
              {NEW_CASE_TEXT}
            </TextTypes>
            <TextTypes
              type='small'
              color={COLOURS.white}
              customStyle={actionSubtitle}
            >
              {NEW_CASE_SUBTITLE}
            </TextTypes>
          </TouchableOpacity>

          {/* OCR & Diagnostics */}
          <TouchableOpacity
            style={[actionCard, secondaryActionCard]}
            onPress={onOCRDiagnostics}
            activeOpacity={0.8}
          >
            <View style={actionIconContainer}>
              <Clipboard width={27} height={27} color={COLOURS.primary} />
            </View>
            <TextTypes
              type='h5'
              color={COLOURS.primary}
              customStyle={actionTitle}
            >
              {OCR_DIAGNOSTICS_TEXT}
            </TextTypes>
            <TextTypes
              type='small'
              color={COLOURS.gray30}
              customStyle={actionSubtitle}
            >
              {OCR_DIAGNOSTICS_SUBTITLE}
            </TextTypes>
          </TouchableOpacity>
        </View>

        {/* Secondary Actions Row */}
        <View style={actionsGrid}>
          {/* Specialist Referral */}
          <TouchableOpacity
            style={[actionCard, secondaryActionCard]}
            onPress={onSpecialistReferral}
            activeOpacity={0.8}
          >
            <View style={actionIconContainer}>
              <AddVideo width={27} height={27} color={COLOURS.primary} />
            </View>
            <TextTypes
              type='h5'
              color={COLOURS.primary}
              customStyle={actionTitle}
            >
              {SPECIALIST_REFERRAL_TEXT}
            </TextTypes>
            <TextTypes
              type='small'
              color={COLOURS.gray30}
              customStyle={actionSubtitle}
            >
              {SPECIALIST_REFERRAL_SUBTITLE}
            </TextTypes>
          </TouchableOpacity>

          {/* Invite a Client */}
          <TouchableOpacity
            style={[actionCard, secondaryActionCard]}
            onPress={onInviteClient}
            activeOpacity={0.8}
          >
            <View style={actionIconContainer}>
              <AddUser width={24} height={24} color={COLOURS.primary} />
            </View>
            <TextTypes
              type='h5'
              color={COLOURS.primary}
              customStyle={actionTitle}
            >
              {INVITE_CLIENT_TEXT}
            </TextTypes>
            <TextTypes
              type='small'
              color={COLOURS.gray30}
              customStyle={actionSubtitle}
            >
              {INVITE_CLIENT_SUBTITLE}
            </TextTypes>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}
