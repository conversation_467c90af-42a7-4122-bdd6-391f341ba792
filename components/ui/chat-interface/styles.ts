import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  chatView: {
    flex: 1,
  },
  footerContainer: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    borderRadius: 16,
    marginVertical: 4,
    padding: 12,
  },
  markDownTextStyle: { color: COLOURS.textBlack, fontSize: 16 },
  loaderContainer: { alignSelf: 'center', marginTop: 8 },
  footerLoaderContainer: {
    alignSelf: 'center',
    marginVertical: 4,
    marginLeft: 0,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  listStyle: { padding: 16, paddingBottom: 6 },
  bottomView: {
    backgroundColor: COLOURS.white,
    paddingHorizontal: 20,
    paddingTop: 15,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    borderTopEndRadius: 25,
    borderTopStartRadius: 25,
  },
  inputBarWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  inputBar: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: COLOURS.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLOURS.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    minHeight: 56,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    paddingHorizontal: 8,
    paddingVertical: 12,
    color: COLOURS.textBlack,
  },
  actionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rowButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sendIcon: {
    marginLeft: 8,
  },
  quickActionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  quickActionsScrollView: {
    paddingHorizontal: 0,
  },
  quickActionsContent: {
    flexDirection: 'row',
    gap: 8,
  },
  quickActionButton: {
    backgroundColor: COLOURS.white,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    maxWidth: 200,
  },
  quickActionButtonText: {
    textAlign: 'center',
    lineHeight: 18,
  },
  selectedFilesContainer: {
    paddingBottom: 12,
  },
  selectedFilesContent: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 4,
  },
  selectedFileWrapper: {
    position: 'relative',
  },
  selectedFile: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: COLOURS.borderColor,
  },
  selectedDocumentFile: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  documentIconSmall: {
    fontSize: 20,
    marginBottom: 2,
  },
  fileNameSmall: {
    textAlign: 'center',
    fontSize: 8,
    lineHeight: 10,
  },
  removeFileButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 14,
    height: 14,
    borderRadius: 12,
    backgroundColor: COLOURS.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default styles;