import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  messageContainer: {
    borderRadius: 16,
    marginVertical: 4,
    padding: 12,
  },
  youText: {
    marginBottom: 5,
  },
  messageText: {
    marginTop: 6,
  },
  filesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  fileItemContainer: {
    borderRadius: 8,
  },
  messageFile: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  documentPreview: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  documentIcon: {
    fontSize: 32,
    marginBottom: 4,
  },
  fileName: {
    textAlign: 'center',
    fontSize: 10,
    lineHeight: 12,
  },
});

export default styles;