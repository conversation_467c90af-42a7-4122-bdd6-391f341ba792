import React, { useState } from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import TextTypes from '@/components/text-types';
import type { ChatItem } from '@/types/chat';
import { COLOURS } from '@/constants/colours';
import { isImageFile, openDocument } from '@/lib/utils/fileUtils';
import FullScreenImageViewer from '@/components/ui/full-screen-image-viewer';
import styles from './styles';

interface UserMessageProps {
  item: ChatItem;
  COLOURS: typeof COLOURS;
}

export default function UserMessage({ item, COLOURS }: UserMessageProps) {
  const [fullScreenImage, setFullScreenImage] = useState<string | null>(null);

  const handleDocumentPress = async (file: any) => {
    if (!isImageFile(file.fileType, file.fileName)) {
      await openDocument(file);
    }
  };

  const handleImagePress = (imageUri: string) => {
    setFullScreenImage(imageUri);
  };

  const closeFullScreenImage = () => {
    setFullScreenImage(null);
  };

  return (
    <View
      style={[
        {
          alignSelf: 'flex-end',
          backgroundColor: COLOURS.orange,
        },
        styles.messageContainer,
        { borderTopRightRadius: 0 },
      ]}
    >
      <TextTypes customStyle={styles.youText} type='errorText' color={COLOURS.youText}>
        You
      </TextTypes>

      {/* Display files if any */}
      {item.files && item.files.length > 0 && (
        <View style={styles.filesContainer}>
          {item.files.map((file) => (
            <View key={file.id} style={styles.fileItemContainer}>
              {isImageFile(file.fileType, file.fileName) ? (
                <TouchableOpacity
                  onPress={() => handleImagePress(file.uri)}
                  activeOpacity={0.8}
                >
                  <Image
                    source={{ uri: file.uri }}
                    style={styles.messageFile}
                    resizeMode="cover"
                    onLoad={() => console.log('Image loaded successfully:', file.fileName)}
                    onError={(error) => console.log('Image load error:', error, 'for file:', file.fileName)}
                  />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={styles.documentPreview}
                  onPress={() => handleDocumentPress(file)}
                  activeOpacity={0.7}
                >
                  <TextTypes
                    type="small"
                    color={COLOURS.textBlack}
                    style={styles.documentIcon}
                  >
                    📄
                  </TextTypes>
                  <TextTypes
                    type="small"
                    color={COLOURS.textBlack}
                    style={styles.fileName}
                    numberOfLines={2}
                  >
                    {file.fileName}
                  </TextTypes>
                </TouchableOpacity>
              )}
            </View>
          ))}
        </View>
      )}

      {/* Display text if any */}
      {item.text && (
        <TextTypes
          style={styles.messageText}
          type={'body2'}
          color={COLOURS.textBlack}
        >
          {item.text}
        </TextTypes>
      )}

      {/* Full Screen Image Viewer */}
      <FullScreenImageViewer
        visible={!!fullScreenImage}
        imageUri={fullScreenImage || ''}
        onClose={closeFullScreenImage}
      />
    </View>
  );
}
