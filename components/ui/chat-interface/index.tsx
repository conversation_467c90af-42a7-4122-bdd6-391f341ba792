import { useState } from 'react';
import type { RefObject } from 'react';
import {
  FlatList,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import Markdown from 'react-native-markdown-display';
import type { EdgeInsets } from 'react-native-safe-area-context';

import SvgAdd from '@/components/icons/Add';
import SvgMic from '@/components/icons/Mic';
import SvgSend from '@/components/icons/Send';
import TextTypes from '@/components/text-types';
import Loader from '@/components/ui/Loader';
import FullScreenImageViewer from '@/components/ui/full-screen-image-viewer';
import { COLOURS } from '@/constants/colours';
import type { ChatItem } from '@/types/chat';
import { isImageFile, openDocument } from '@/lib/utils/fileUtils';

import AiMessage from './ai-message';
import styles from './styles';
import UserMessage from './user-message';
import { Cancel } from '@/components/icons';

interface ChatInterfaceProps {
  messages: ChatItem[];
  onSend: (input: string) => void;
  loading: boolean;
  animatingMessage: string | null;
  isTyping: boolean;
  copyFeedbackId: string | null;
  handleCopy: (text: string, id: string) => void;
  insects: EdgeInsets;
  COLOURS: typeof COLOURS;
  flatListRef: RefObject<FlatList<any>>;
  scrollToBottom: () => void;
  quickActions: string[];
  onQuickAction: (action: string) => void;
  onAddPress: () => void;
  selectedFiles?: Array<{
    uri: string;
    fileName: string;
    fileType: string;
    id: string;
  }>;
  onRemoveFile?: (fileId: string) => void;
}

export default function ChatInterface({
  messages,
  onSend,
  loading,
  animatingMessage,
  isTyping,
  copyFeedbackId,
  handleCopy,
  insects,
  COLOURS,
  flatListRef,
  scrollToBottom,
  quickActions,
  onQuickAction,
  onAddPress,
  selectedFiles = [],
  onRemoveFile,
}: ChatInterfaceProps) {
  const [input, setInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [fullScreenImage, setFullScreenImage] = useState<string | null>(null);

  const handleDocumentPress = async (file: any) => {
    if (!isImageFile(file.fileType, file.fileName)) {
      await openDocument(file);
    }
  };

  const handleImagePress = (imageUri: string) => {
    setFullScreenImage(imageUri);
  };

  const closeFullScreenImage = () => {
    setFullScreenImage(null);
  };

  const handleSendInternal = () => {
    if ((!input.trim() && selectedFiles.length === 0) || loading) return;
    onSend(input);
    setInput('');
  };

  return (
    <>
      <View style={styles.chatView}>
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={({ item }: any) =>
            item.sender === 'user' ? (
              <UserMessage item={item} COLOURS={COLOURS} />
            ) : (
              <AiMessage
                item={item}
                COLOURS={COLOURS}
                copyFeedbackId={copyFeedbackId}
                handleCopy={handleCopy}
              />
            )
          }
          keyExtractor={(item) => item.id}
          onContentSizeChange={scrollToBottom}
          onLayout={scrollToBottom}
          ListFooterComponent={() => (
            <>
              {animatingMessage && (
                <View style={styles.footerContainer}>
                  <Markdown style={{ body: styles.markDownTextStyle }}>
                    {animatingMessage}
                  </Markdown>
                  {isTyping && (
                    <View style={styles.loaderContainer}>
                      <Loader />
                    </View>
                  )}
                </View>
              )}
              {loading && !animatingMessage && (
                <View style={styles.footerLoaderContainer}>
                  <Loader />
                </View>
              )}
            </>
          )}
          contentContainerStyle={styles.listStyle}
        />
      </View>

      {/* Quick Action Buttons */}
      {quickActions.length > 0 && messages.length == 0 && (
        <View style={styles.quickActionsContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.quickActionsContent}
            style={styles.quickActionsScrollView}
          >
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickActionButton}
                onPress={() => onQuickAction(action)}
                activeOpacity={0.8}
              >
                <TextTypes
                  type='label2'
                  color={COLOURS.secondaryTint}
                  style={styles.quickActionButtonText}
                  numberOfLines={2}
                >
                  {action}
                </TextTypes>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      <View style={[styles.bottomView, { paddingBottom: insects.bottom + 10 }]}>
        {/* Selected Files Preview */}
        {selectedFiles.length > 0 && (
          <View style={styles.selectedFilesContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.selectedFilesContent}
            >
              {selectedFiles.map((file) => {
                return (
                <View key={file.id} style={styles.selectedFileWrapper}>
                  {isImageFile(file.fileType, file.fileName) ? (
                    <TouchableOpacity
                      onPress={() => handleImagePress(file.uri)}
                      activeOpacity={0.8}
                    >
                      <Image
                        source={{ uri: file.uri }}
                        style={styles.selectedFile}
                        onLoad={() => console.log('Selected image loaded:', file.fileName)}
                        onError={(error) => console.log('Selected image error:', error, 'for file:', file.fileName)}
                      />
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      style={styles.selectedDocumentFile}
                      onPress={() => handleDocumentPress(file)}
                      activeOpacity={0.7}
                    >
                      <TextTypes type="small" color={COLOURS.textBlack} style={styles.documentIconSmall}>
                        📄
                      </TextTypes>
                      <TextTypes
                        type="small"
                        color={COLOURS.textBlack}
                        style={styles.fileNameSmall}
                        numberOfLines={2}
                      >
                        {file.fileName}
                      </TextTypes>
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity
                    style={styles.removeFileButton}
                    onPress={() => onRemoveFile?.(file.id)}
                    activeOpacity={0.7}
                  >
                    <Cancel width={16} height={16} color={COLOURS.whiteColor} />
                  </TouchableOpacity>
                </View>
                );
              })}
            </ScrollView>
          </View>
        )}

        <View style={styles.inputBarWrapper}>
          <View
            style={[
              styles.inputBar,
              {
                borderColor: isFocused ? COLOURS.primary : COLOURS.borderColor,
              },
            ]}
          >
            <TextInput
              style={styles.textInput}
              placeholder='Ask VetAssist'
              value={input}
              onChangeText={setInput}
              multiline
              selectionColor={COLOURS.primary}
              placeholderTextColor={COLOURS.placeholderText}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
            />
          </View>
        </View>
        <View style={styles.actionButtonContainer}>
          <TouchableOpacity
            onPress={onAddPress}
            activeOpacity={0.7}
          >
            <SvgAdd color={COLOURS.primary} />
          </TouchableOpacity>
          <View style={styles.rowButton}>
            <SvgMic color={COLOURS.primary} />
            <TouchableOpacity
              onPress={handleSendInternal}
              style={styles.sendIcon}
              activeOpacity={(input.trim() || selectedFiles.length > 0) ? 0.7 : 1}
              disabled={(!input.trim() && selectedFiles.length === 0) || loading}
            >
              <SvgSend
                color={(input || selectedFiles.length > 0) ? COLOURS.whiteColor : COLOURS.borderColor}
                bgColor={(input || selectedFiles.length > 0) ? COLOURS.primary : COLOURS.grayIcon}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Full Screen Image Viewer */}
      <FullScreenImageViewer
        visible={!!fullScreenImage}
        imageUri={fullScreenImage || ''}
        onClose={closeFullScreenImage}
      />
    </>
  );
}
