import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CloseIcon } from '@/components/icons';
import { COLOURS } from '@/constants/colours';
import styles from './styles';

interface FullScreenImageViewerProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
}

export default function FullScreenImageViewer({
  visible,
  imageUri,
  onClose,
}: FullScreenImageViewerProps) {
  const insets = useSafeAreaInsets();

  const {
    overlay,
    closeButton,
    fullScreenImage,
  } = styles;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <StatusBar backgroundColor={COLOURS.modalBackgroundDark} barStyle="light-content" />
      <View style={overlay}>
        <TouchableOpacity
          style={[closeButton, { top: insets.top + 16 }]}
          onPress={onClose}
          activeOpacity={0.8}
        >
          <CloseIcon color={COLOURS.white} width={24} height={24} />
        </TouchableOpacity>

        <Image
          source={{ uri: imageUri }}
          style={fullScreenImage}
          resizeMode="contain"
          onError={(error) => console.log('Full screen image load error:', error)}
        />
      </View>
    </Modal>
  );
}
